var ie=Object.defineProperty;var le=(n,e,s)=>e in n?ie(n,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):n[e]=s;var C=(n,e,s)=>le(n,typeof e!="symbol"?e+"":e,s);import{f as $,b as v,l as L,A as Z,u as K,C as h,X as Q,Y as B,m as z,Z as ce,K as G,_ as w,$ as W,D as re,J as U,a7 as de,F as ue,G as X,N as he,a3 as D,t as S,Q as H,ab as k,L as ge,I as j,H as ve,M as R,R as me,P as V,W as Y,w as pe,aA as E,v as fe,a as J}from"./SpinnerAugment-BY2Lraps.js";import{B as Ce,a as we}from"./trash-can-Col-hlUX.js";import{C as ye,b as d,h as q}from"./IconButtonAugment-B8y0FMb_.js";import{a as be}from"./BaseTextInput-C64uUToe.js";import{R as A}from"./message-broker-BauNv3yh.js";import{i as T}from"./index-78K9HN9C.js";var Se=$('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor"></path></svg>');function Ue(n){var e=Se();v(n,e)}var xe=U("<div><!></div>");const Ie={Root:we,IconButton:function(n,e){const s=L(e,["children","$$slots","$$events","$$legacy"]),t=L(s,["color","highContrast","disabled"]);Z(e,!1);const c=z(),m=z(),r=K(Ce.CONTEXT_KEY);let u=h(e,"color",24,()=>{return a=r.color,l="neutral",typeof a=="string"&&["accent","neutral","error","success","warning","info"].includes(a)?a:l;var a,l}),o=h(e,"highContrast",8,!1),i=h(e,"disabled",8,!1),g=r.size===0?.5:r.size;Q(()=>(w(c),w(m),G(t)),()=>{B(c,t.class),B(m,ce(t,["class"]))}),W(),re();var b=xe(),_=S(b);const p=D(()=>`c-badge-icon-btn__base-btn ${w(c)}`);ye(_,de({get size(){return g},variant:"ghost",get color(){return u()},get highContrast(){return o()},get disabled(){return i()},get class(){return w(p)}},()=>w(m),{$$events:{click(a){d.call(this,e,a)},keyup(a){d.call(this,e,a)},keydown(a){d.call(this,e,a)},mousedown(a){d.call(this,e,a)},mouseover(a){d.call(this,e,a)},focus(a){d.call(this,e,a)},mouseleave(a){d.call(this,e,a)},blur(a){d.call(this,e,a)},contextmenu(a){d.call(this,e,a)}},children:(a,l)=>{var f=ue(),x=X(f);he(x,e,"default",{},null),v(a,f)},$$slots:{default:!0}})),H(()=>k(b,1,ge(()=>`c-badge-icon-btn c-badge-icon-btn--${r.variant} c-badge-icon-btn--size-${g}`),"svelte-1im94um")),v(n,b),j()}};var ke=U("<span> </span> <span> </span>",1),Le=U('<label><!> <input type="checkbox" role="switch"/></label>');function Oe(n,e){Z(e,!1);const s=z();let t=h(e,"checked",12,!1),c=h(e,"disabled",8,!1),m=h(e,"size",8,2),r=h(e,"ariaLabel",24,()=>{}),u=h(e,"onText",24,()=>{}),o=h(e,"offText",24,()=>{});Q(()=>(G(u()),G(o())),()=>{B(s,u()||o())}),W();var i=Le();let g;var b=S(i),_=l=>{var f=ke(),x=X(f);let I;var se=S(x),O=R(x,2);let P;var ne=S(O);H((ae,oe)=>{I=k(x,1,"c-toggle-text c-toggle-text--off svelte-xr5g0k",null,I,ae),Y(se,o()||""),P=k(O,1,"c-toggle-text c-toggle-text--on svelte-xr5g0k",null,P,oe),Y(ne,u()||"")},[()=>({visible:!t()&&o()}),()=>({visible:t()&&u()})],D),v(l,f)};ve(b,l=>{w(s)&&l(_)});var p=R(b,2);let a;H((l,f)=>{g=k(i,1,`c-toggle-track c-toggle-track-size--${m()??""}`,"svelte-xr5g0k",g,l),a=k(p,1,"c-toggle-input svelte-xr5g0k",null,a,f),p.disabled=c(),me(p,"aria-label",r())},[()=>({checked:t(),disabled:c(),"has-text":w(s)}),()=>({disabled:c()})],D),be(p,t),V("keydown",p,function(l){c()||l.key!=="Enter"&&l.key!==" "||(l.preventDefault(),t(!t()))}),V("change",i,function(l){d.call(this,e,l)}),v(n,i),j()}function Pe(n){const{rules:e,workspaceGuidelinesContent:s,contextRules:t=[]}=n,c=e.filter(o=>o.type===A.ALWAYS_ATTACHED).reduce((o,i)=>o+i.content.length+i.path.length,0),m=e.filter(o=>o.type===A.AGENT_REQUESTED).reduce((o,i)=>{var g;return o+100+(((g=i.description)==null?void 0:g.length)??0)+i.path.length},0),r=c+e.filter(o=>o.type===A.MANUAL).filter(o=>t.some(i=>i.path===o.path)).reduce((o,i)=>o+i.content.length+i.path.length,0)+m+s.length,u=n.rulesAndGuidelinesLimit&&r>n.rulesAndGuidelinesLimit;return{totalCharacterCount:r,isOverLimit:u,warningMessage:u&&n.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${r} chars)
        exceeds the limit of ${n.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}const M={enabled:!1,volume:.5},Re={enabled:!0},$e=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href;var ee=(n=>(n.AGENT_COMPLETE="agent-complete",n))(ee||{});const _e={"agent-complete":$e},y=class y{constructor(){C(this,"audioCache",new Map)}static getInstance(){return y._instance||(y._instance=new y),y._instance}retrieveAudioElement(e,s){let t=this.audioCache.get(e);return t?t.volume=s.volume:(t=new Audio,t.src=_e[e],t.volume=s.volume,t.preload="auto",t._isUnlocked=!1,this.audioCache.set(e,t)),t}async playSound(e,s){if(s.enabled)try{const t=this.retrieveAudioElement(e,s);t.currentTime=0,await t.play()}catch(t){if(t instanceof DOMException&&t.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",t)}}async unlockSoundForConfig(e){if(!e.enabled)return;const s=this.retrieveAudioElement("agent-complete",e);if(!s._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),s._isUnlocked=!0}catch(t){console.warn("Failed to unlock sound:",t)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};C(y,"_instance");let N=y;const F=N.getInstance();class Ee{constructor(e){C(this,"_soundSettings",pe(M));C(this,"_isLoaded",!1);C(this,"dispose",()=>{F.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:T.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){E(this._soundSettings).enabled&&F.unlockSoundForConfig(E(this._soundSettings))}async playAgentComplete(){const e=E(this._soundSettings);await F.playSound(ee.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:T.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(M),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:T.updateSoundSettings,data:e}),this._soundSettings.update(s=>({...s,...e}))}catch(s){throw console.error("Failed to update sound settings:",s),s}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const s=Math.max(0,Math.min(1,e));await this.updateSettings({volume:s})}async resetToDefaults(){await this.updateSettings(M)}updateEnabled(e){this.setEnabled(e).catch(s=>{console.error("Failed to update enabled setting:",s)})}updateVolume(e){this.setVolume(e).catch(s=>{console.error("Failed to update volume setting:",s)})}}C(Ee,"key","soundModel");const te="extensionClient";function Ve(n){fe(te,n)}function Ye(){const n=K(te);if(!n)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return n}var Ae=$("<svg><!></svg>");function Ze(n,e){const s=L(e,["children","$$slots","$$events","$$legacy"]);var t=Ae();J(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var c=S(t);q(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',!0),v(n,t)}var Te=$('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg>');function Ke(n){var e=Te();v(n,e)}var Me=$("<svg><!></svg>");function Qe(n,e){const s=L(e,["children","$$slots","$$events","$$legacy"]);var t=Me();J(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...s}));var c=S(t);q(c,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',!0),v(n,t)}export{Ie as B,Ue as C,Re as D,Ke as G,Ze as P,Ee as S,Oe as T,Qe as a,Pe as c,Ye as g,Ve as s};
