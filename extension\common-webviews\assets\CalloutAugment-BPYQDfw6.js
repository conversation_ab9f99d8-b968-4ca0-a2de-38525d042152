import{z as N,l as C,A as T,C as s,X,Y as m,m as q,Z as Y,K as Z,_ as l,$ as j,D as S,J as u,a as _,a0 as k,a1 as w,T as B,G as E,H as F,L as O,M as P,N as b,t as r,b as v,I as Q}from"./SpinnerAugment-BY2Lraps.js";var R=u('<div class="c-callout-icon svelte-1u5qnh6"><!></div>'),U=u('<!> <div class="c-callout-body svelte-1u5qnh6"><!></div>',1),V=u("<div><!></div>");function G(p,a){const y=N(a),x=C(a,["children","$$slots","$$events","$$legacy"]),t=C(x,["color","variant","size","highContrast"]);T(a,!1);const c=q(),o=q();let $=s(a,"color",8,"info"),A=s(a,"variant",8,"soft"),d=s(a,"size",8,2),D=s(a,"highContrast",8,!1);const H=d();X(()=>(l(c),l(o),Z(t)),()=>{m(c,t.class),m(o,Y(t,["class"]))}),j(),S();var n=V();_(n,(i,h)=>({...i,class:`c-callout c-callout--${$()} c-callout--${A()} c-callout--size-${d()} ${l(c)}`,...l(o),[w]:h}),[()=>k($()),()=>({"c-callout--highContrast":D()})],"svelte-1u5qnh6");var I=r(n);B(I,{get size(){return H},children:(i,h)=>{var f=U(),g=E(f),J=e=>{var z=R(),M=r(z);b(M,a,"icon",{},null),v(e,z)};F(g,e=>{O(()=>y.icon)&&e(J)});var K=P(g,2),L=r(K);b(L,a,"default",{},null),v(i,f)},$$slots:{default:!0}}),v(p,n),Q()}export{G as C};
