import{C as b,J as h,H as J,t as o,M as a,Q as O,a3 as gt,ab as D,P as A,b as p,W as et,R as ft,A as dt,B as yt,m,ak as wt,aE as pt,_ as t,Y as c,X as U,K as tt,$ as kt,D as mt,L as At,I as ht,G as bt,al as $t,F as qt,az as Bt}from"./SpinnerAugment-BY2Lraps.js";import{c as B,W as H}from"./IconButtonAugment-B8y0FMb_.js";import{aJ as lt}from"./AugmentMessage-FtcicXdY.js";import{b as xt,a as Ct}from"./BaseTextInput-C64uUToe.js";import{C as Mt,S as Rt}from"./folder-opened-B-IBFaHN.js";import{M as It}from"./message-broker-BauNv3yh.js";import{s as St}from"./chat-model-context-C9JFkoqk.js";import{M as Wt}from"./index-BPm23rLE.js";import"./CalloutAugment-BPYQDfw6.js";import"./CardAugment-BaFOe6RO.js";import"./index-78K9HN9C.js";import"./async-messaging-BnOo7nYC.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CAuiPHBF.js";import"./isObjectLike-DuRpH5zX.js";import"./index-CoHT-xzg.js";import"./diff-operations-TFiyZoZ6.js";import"./svelte-component-DfqKRK9G.js";import"./Filespan-7QsRDmJG.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./keypress-DD1aQVr0.js";import"./await-CoczQRp_.js";import"./OpenFileButton-DL6LWlGL.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-XI3B217g.js";import"./ra-diff-ops-model-3WeVBTrk.js";import"./TextAreaAugment-BkN7aH6v.js";import"./ButtonAugment-BoJU5mQc.js";import"./CollapseButtonAugment-CllkyJKm.js";import"./trash-can-Col-hlUX.js";import"./MaterialIcon-CKuUXxrb.js";import"./feedback-rating-BX5Icwas.js";import"./copy-DdR1jezc.js";import"./ellipsis-BVNflcFA.js";import"./LanguageIcon-BvE8QmB9.js";import"./chevron-down-CVLGkBkY.js";import"./index-ALhsmmIa.js";import"./augment-logo-DFXa-EF4.js";import"./pen-to-square-BKF2K8ly.js";var Dt=h('<div class="header svelte-1894wv4"> </div>'),Pt=h('<div class="container svelte-1894wv4"><!> <div class="buttons svelte-1894wv4"><button type="button">A</button> <button type="button">A</button> <button type="button">A</button> <button type="button">=</button> <button type="button">B</button> <button type="button">B</button> <button type="button">B</button></div></div>');function nt(K,x){let i=b(x,"selected",12,null),C=b(x,"question",8,null);function v(w){i(w)}var e=Pt(),d=o(e),P=w=>{var E=Dt(),G=o(E);O(()=>et(G,C())),p(w,E)};J(d,w=>{C()&&w(P)});var M=a(d,2),g=o(M);let f;var r=a(g,2);let $;var y=a(r,2);let s;var R=a(y,2);let X;var _=a(R,2);let Y;var z=a(_,2);let Q;var j=a(z,2);let Z;O((w,E,G,at,st,l,u)=>{f=D(g,1,"button large svelte-1894wv4",null,f,w),$=D(r,1,"button medium svelte-1894wv4",null,$,E),s=D(y,1,"button small svelte-1894wv4",null,s,G),X=D(R,1,"button equal svelte-1894wv4",null,X,at),Y=D(_,1,"button small svelte-1894wv4",null,Y,st),Q=D(z,1,"button medium svelte-1894wv4",null,Q,l),Z=D(j,1,"button large svelte-1894wv4",null,Z,u)},[()=>({highlighted:i()==="A3"}),()=>({highlighted:i()==="A2"}),()=>({highlighted:i()==="A1"}),()=>({highlighted:i()==="="}),()=>({highlighted:i()==="B1"}),()=>({highlighted:i()==="B2"}),()=>({highlighted:i()==="B3"})],gt),A("click",g,()=>v("A3")),A("click",r,()=>v("A2")),A("click",y,()=>v("A1")),A("click",R,()=>v("=")),A("click",_,()=>v("B1")),A("click",z,()=>v("B2")),A("click",j,()=>v("B3")),p(K,e)}var _t=h('<div class="question svelte-1i0f73l"> </div>'),zt=h('<div class="container svelte-1i0f73l"><!> <textarea class="input svelte-1i0f73l" rows="3"></textarea></div>'),Et=h('<button class="button svelte-2k5n"> </button>'),Ft=h("<div> </div>"),Lt=h('<div class="container svelte-n0uy88"><!> <label class="custom-checkbox svelte-n0uy88"><input type="checkbox" class="svelte-n0uy88"/> <span class="svelte-n0uy88"></span></label></div>'),Ot=h("<!> <!> <!> <!> <!> <!>",1),Ht=h("<p>Streaming in progress... Please wait for both responses to complete.</p>"),Jt=h('<main><div class="l-pref svelte-751nif"><h1 class="svelte-751nif">Input message</h1> <!> <hr class="l-side-by-side svelte-751nif"/> <div class="l-side-by-side svelte-751nif"><div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option A</h1> <!></div> <div class="divider svelte-751nif"></div> <div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option B</h1> <!></div></div> <hr class="svelte-751nif"/> <!></div></main>');function Kt(K,x){dt(x,!1);const i=m(),C=m(),v=m();let e=b(x,"inputData",8);const d=yt();let P=new Mt(new It(B),B,new Rt);St(P);let M=m(null),g=m(null),f=null,r=m(null),$=m(""),y=m(!1),s=m({a:null,b:null}),R=m(e().data.a.response.length>0&&e().data.b.response.length>0);function X(){if(f="=",t(r)===null)return void d("notify","Overall rating is required");const l={overallRating:t(r),formattingRating:t(M)||"=",hallucinationRating:f||"=",instructionFollowingRating:t(g)||"=",isHighQuality:t(y),textFeedback:t($)};d("result",l)}wt(()=>{window.addEventListener("message",l=>{const u=l.data;u.type===H.chatModelReply?(u.stream==="A"?pt(s,t(s).a=u.data.text):u.stream==="B"&&pt(s,t(s).b=u.data.text),c(s,t(s))):u.type===H.chatStreamDone&&c(R,!0)})}),U(()=>t(r),()=>{var l;c(i,(l=t(r))==="="||l===null?"Is this a high quality comparison?":`Are you completely happy with response '${l.startsWith("A")?"A":"B"}'?`)}),U(()=>(t(s),tt(e())),()=>{c(C,t(s).a!==null?t(s).a:e().data.a.response)}),U(()=>(t(s),tt(e())),()=>{c(v,t(s).b!==null?t(s).b:e().data.b.response)}),U(()=>tt(e()),()=>{c(R,e().data.a.response.length>0&&e().data.b.response.length>0)}),kt(),mt();var _=Jt(),Y=o(_),z=a(o(Y),2);lt(z,{get markdown(){return tt(e()),At(()=>e().data.a.message)}});var Q=a(z,4),j=o(Q),Z=a(o(j),2);lt(Z,{get markdown(){return t(C)}});var w=a(j,4),E=a(o(w),2);lt(E,{get markdown(){return t(v)}});var G=a(Q,4),at=l=>{var u=Ot(),ot=bt(u);nt(ot,{question:"Which response is formatted better? (e.g. level of detail style, structure)?",get selected(){return t(M)},set selected(n){c(M,n)},$$legacy:!0});var rt=a(ot,2);nt(rt,{question:"Which response follows your instruction better?",get selected(){return t(g)},set selected(n){c(g,n)},$$legacy:!0});var ut=a(rt,2);nt(ut,{question:"Which response is better overall?",get selected(){return t(r)},set selected(n){c(r,n)},$$legacy:!0});var vt=a(ut,2);(function(n,k){let N=b(k,"isChecked",12,!1),I=b(k,"question",8,null);var q=Lt(),S=o(q),F=W=>{var V=Ft(),it=o(V);O(()=>et(it,I())),p(W,V)};J(S,W=>{I()&&W(F)});var L=a(S,2),T=o(L);Ct(T,N),p(n,q)})(vt,{get question(){return t(i)},get isChecked(){return t(y)},set isChecked(n){c(y,n)},$$legacy:!0});var ct=a(vt,2);(function(n,k){let N=b(k,"value",12,""),I=b(k,"question",8,null),q=b(k,"placeholder",8,"");var S=zt(),F=o(S),L=W=>{var V=_t(),it=o(V);O(()=>et(it,I())),p(W,V)};J(F,W=>{I()&&W(L)});var T=a(F,2);O(()=>ft(T,"placeholder",q())),xt(T,N),p(n,S)})(ct,{question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions.",get value(){return t($)},set value(n){c($,n)},$$legacy:!0}),function(n,k){let N=b(k,"label",8,"Submit"),I=b(k,"onClick",8);var q=Et(),S=o(q);O(()=>et(S,N())),A("click",q,function(...F){var L;(L=I())==null||L.apply(this,F)}),p(n,q)}(a(ct,2),{label:"Submit",onClick:X}),p(l,u)},st=l=>{var u=Ht();p(l,u)};J(G,l=>{t(R)?l(at):l(st,!1)}),p(K,_),ht()}var Qt=h("<main><!></main>");function jt(K,x){dt(x,!1);let i=m();function C(e){const d=e.detail;B.postMessage({type:H.preferenceResultMessage,data:d})}function v(e){B.postMessage({type:H.preferenceNotify,data:e.detail})}B.postMessage({type:H.preferencePanelLoaded}),mt(),A("message",$t,function(e){const d=e.data;d.type===H.preferenceInit&&c(i,d.data)}),Wt.Root(K,{children:(e,d)=>{var P=Qt(),M=o(P),g=f=>{var r=qt(),$=bt(r),y=s=>{Kt(s,{get inputData(){return t(i)},$$events:{result:C,notify:v}})};J($,s=>{t(i).type==="Chat"&&s(y)}),p(f,r)};J(M,f=>{t(i)&&f(g)}),p(e,P)},$$slots:{default:!0}}),ht()}(async function(){B&&B.initialize&&await B.initialize(),Bt(jt,{target:document.getElementById("app")})})();
