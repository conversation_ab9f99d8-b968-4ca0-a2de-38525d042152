import{l as Ze,f as Ne,a as Ye,t as _,b as i,A as Se,C as x,m as O,a6 as je,D as ye,J as h,T as se,V as ae,Q as J,W as le,M as p,G as K,H as P,L as o,K as y,_ as e,I as ke,Y as A,X as V,$ as Ce,a3 as b,ab as Ge,R as et,F as tt,P as Je,u as Ee,a4 as Ke,a2 as Ve,ay as st,v as Qe,ak as at,al as nt,az as rt}from"./SpinnerAugment-BY2Lraps.js";import"./design-system-init-DkEuonq_.js";/* empty css                                */import{h as it,I as Me,e as ue,i as ot,c as lt}from"./IconButtonAugment-B8y0FMb_.js";import{M as dt}from"./message-broker-BauNv3yh.js";import{S as ct,T as vt,a as Ue,b as gt,c as Be,d as ut,v as mt,e as pt}from"./StatusIndicator-CISyK7_6.js";import{a as Y,s as wt,R as Oe}from"./index-C4gKbsWy.js";import{T as qe,a as oe,C as ft}from"./CardAugment-BaFOe6RO.js";import{C as ht}from"./CalloutAugment-BPYQDfw6.js";import{E as _t}from"./exclamation-triangle-BF0Ta3I3.js";import{d as $t,s as St,R as Te}from"./remote-agents-client-XI3B217g.js";import{A as yt}from"./augment-logo-DFXa-EF4.js";import"./async-messaging-BnOo7nYC.js";import"./types-CGlLNakm.js";var kt=Ne("<svg><!></svg>"),xt=h(" <!>",1),bt=h('<div class="agent-card-footer svelte-1qwlkoj"><!> <div class="time-container"><!></div></div>'),At=h('<div class="task-text-container svelte-1tatwxk"><!></div>'),Pt=h('<div class="task-status-indicator svelte-1tatwxk"><!></div>'),Rt=h('<div class="task-item svelte-1tatwxk"><div></div> <!> <!></div>'),zt=h(' <button class="error-dismiss svelte-1bxdvw4" aria-label="Dismiss error">×</button>',1),Ht=h('<div class="deletion-error svelte-1bxdvw4"><!></div>'),Ft=h('<span class="setup-script-title svelte-1bxdvw4">Generate a setup script</span>'),It=h('<div class="setup-script-title-container svelte-1bxdvw4"><div class="setup-script-badge svelte-1bxdvw4"><!></div> <!></div>'),Ot=h('<div class="tasks-list svelte-1bxdvw4"></div>'),qt=h('<div class="card-header svelte-1bxdvw4"><div class="session-summary-container svelte-1bxdvw4"><!></div> <div class="card-info"><!></div></div> <div class="card-content svelte-1bxdvw4"><!></div> <div class="card-actions svelte-1bxdvw4"><!> <!> <!></div> <!>',1),Tt=h("<div><!> <!></div>");function Ct(de,S){Se(S,!1);const q=O(),H=O(),R=O(),M=O();let c=x(S,"agent",8),X=x(S,"selected",8,!1),F=x(S,"isPinned",8,!1),I=x(S,"onSelect",8),B=x(S,"onDelete",8),T=x(S,"deletionError",12,null),j=x(S,"isDeleting",8,!1),v=x(S,"onTogglePinned",24,()=>{}),E=x(S,"sshConfig",24,()=>{});function G(){T(null)}je(()=>{G()}),V(()=>(e(q),e(H),y(E())),()=>{var $;$=E()||{onSSH:()=>Promise.resolve(!1),canSSH:!1},A(q,$.onSSH),A(H,$.canSSH)}),V(()=>y(c()),()=>{A(R,c().turn_summaries||[])}),V(()=>{},()=>{A(M,!0)}),Ce(),ye();var d=Tt();let ce;var me=_(d),g=$=>{var ne=Ht(),k=_(ne);ht(k,{variant:"soft",color:"error",size:1,children:(s,l)=>{var Q=zt(),ee=K(Q),D=p(ee);J(()=>le(ee,`${T()??""} `)),Je("click",D,G),i(s,Q)},$$slots:{default:!0,icon:(s,l)=>{_t(s,{slot:"icon"})}}}),i($,ne)};P(me,$=>{T()&&$(g)});var Z=p(me,2);ft(Z,{variant:"surface",size:2,interactive:!0,class:"agent-card",$$events:{click:()=>I()(c().remote_agent_id),keydown:$=>$.key==="Enter"&&I()(c().remote_agent_id)},children:($,ne)=>{var k=qt(),s=K(k),l=_(s),Q=_(l),ee=a=>{var r=It(),f=_(r),n=_(f);Ue(n);var t=p(f,2);se(t,{size:2,weight:"medium",children:(m,u)=>{var U=Ft();i(m,U)},$$slots:{default:!0}}),i(a,r)},D=a=>{se(a,{size:2,weight:"medium",class:"session-text",children:(r,f)=>{var n=ae();J(()=>le(n,(y(c()),o(()=>c().session_summary)))),i(r,n)},$$slots:{default:!0}})};P(Q,a=>{y(c()),o(()=>c().is_setup_script_agent)?a(ee):a(D,!1)});var L=p(l,2),ve=_(L);ct(ve,{get status(){return y(c()),o(()=>c().status)},get workspaceStatus(){return y(c()),o(()=>c().workspace_status)},isExpanded:!0,get hasUpdates(){return y(c()),o(()=>c().has_updates)}});var re=p(s,2),xe=_(re),be=a=>{var r=Ot();ue(r,5,()=>(e(R),o(()=>e(R).slice(0,3))),ot,(f,n)=>{(function(t,m){Se(m,!1);const u=O();let U=x(m,"text",8),W=x(m,"status",8,"info");V(()=>y(W()),()=>{A(u,function(C){switch(C){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(W()))}),Ce(),ye();var pe=Rt(),we=_(pe),z=p(we,2);const Pe=b(()=>(y(oe),o(()=>[oe.Hover])));qe(z,{get content(){return U()},get triggerOn(){return e(Pe)},maxWidth:"400px",children:(C,ge)=>{var he=At(),N=_(he);se(N,{size:1,color:"secondary",children:(ie,Xe)=>{var Re=ae();J(()=>le(Re,U())),i(ie,Re)},$$slots:{default:!0}}),i(C,he)},$$slots:{default:!0}});var te=p(z,2),fe=C=>{var ge=Pt(),he=_(ge);const N=b(()=>W()==="error"?"error":"neutral");se(he,{size:1,get color(){return e(N)},children:(ie,Xe)=>{var Re=ae();J(()=>le(Re,W()==="error"?"!":W()==="warning"?"⚠":"")),i(ie,Re)},$$slots:{default:!0}}),i(C,ge)};P(te,C=>{W()!=="error"&&W()!=="warning"||C(fe)}),J(()=>Ge(we,1,`bullet-point ${e(u)??""}`,"svelte-1tatwxk")),i(t,pe),ke()})(f,{get text(){return e(n)},status:"success"})}),i(a,r)};P(xe,a=>{e(R),o(()=>e(R).length>0)&&a(be)});var Ae=p(re,2),ze=_(Ae),He=a=>{const r=b(()=>F()?"Unpin agent":"Pin agent"),f=b(()=>(y(oe),o(()=>[oe.Hover])));qe(a,{get content(){return e(r)},get triggerOn(){return e(f)},side:"top",children:(n,t)=>{Me(n,{variant:"ghost",color:"neutral",size:1,$$events:{click:m=>{m.stopPropagation(),v()()}},children:(m,u)=>{var U=tt(),W=K(U),pe=z=>{(function(Pe,te){const fe=Ze(te,["children","$$slots","$$events","$$legacy"]);var C=kt();Ye(C,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 640 512",...fe}));var ge=_(C);it(ge,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',!0),i(Pe,C)})(z,{})},we=z=>{gt(z,{})};P(W,z=>{F()?z(pe):z(we,!1)}),i(m,U)},$$slots:{default:!0}})},$$slots:{default:!0}})};P(ze,a=>{v()&&a(He)});var Fe=p(ze,2),Ie=a=>{const r=b(()=>(y(oe),o(()=>[oe.Hover])));qe(a,{content:"SSH to agent",get triggerOn(){return e(r)},side:"top",children:(f,n)=>{const t=b(()=>!e(H)),m=b(()=>e(H)?"SSH to agent":"SSH to agent (agent must be running or idle)");Me(f,{get disabled(){return e(t)},variant:"ghost",color:"neutral",size:1,get title(){return e(m)},$$events:{click:u=>{u.stopPropagation(),e(q)()}},children:(u,U)=>{Ue(u)},$$slots:{default:!0}})},$$slots:{default:!0}})};P(Fe,a=>{E()&&a(Ie)});var De=p(Fe,2);const Le=b(()=>(y(oe),o(()=>[oe.Hover])));qe(De,{content:"Delete agent",get triggerOn(){return e(Le)},side:"top",children:(a,r)=>{const f=b(()=>j()?"Deleting agent...":"Delete agent");Me(a,{variant:"ghost",color:"neutral",size:1,get disabled(){return j()},get title(){return e(f)},$$events:{click:n=>{n.stopPropagation(),B()()}},children:(n,t)=>{vt(n)},$$slots:{default:!0}})},$$slots:{default:!0}});var We=p(Ae,2);const w=b(()=>(y(c()),o(()=>c().updated_at||c().started_at)));(function(a,r){Se(r,!1);let f=x(r,"isRemote",8,!1),n=x(r,"status",8),t=x(r,"timestamp",8),m=O($t(t()));const u=St(t(),z=>{A(m,z)});je(()=>{u()}),ye();var U=bt(),W=_(U);se(W,{size:1,color:"secondary",class:"location-text",children:(z,Pe)=>{var te=ae();J(()=>le(te,f()?"Running in the cloud":"Running locally")),i(z,te)},$$slots:{default:!0}});var pe=p(W,2),we=_(pe);se(we,{size:1,color:"secondary",class:"time-text",children:(z,Pe)=>{var te=xt(),fe=K(te),C=p(fe),ge=N=>{var ie=ae();J(()=>le(ie,e(m))),i(N,ie)},he=N=>{var ie=ae("Unknown time");i(N,ie)};P(C,N=>{t()?N(ge):N(he,!1)}),J(()=>le(fe,`${y(n()),y(Y),o(()=>n()===Y.agentRunning?"Last updated":"Started")??""} `)),i(z,te)},$$slots:{default:!0}}),i(a,U),ke()})(We,{get isRemote(){return e(M)},get status(){return y(c()),o(()=>c().status)},get timestamp(){return e(w)}}),J(()=>et(l,"title",(y(c()),o(()=>c().is_setup_script_agent?"Generate a setup script":c().session_summary)))),i($,k)},$$slots:{default:!0}}),J($=>ce=Ge(d,1,"card-wrapper svelte-1bxdvw4",null,ce,$),[()=>({"selected-card":X(),"setup-script-card":c().is_setup_script_agent,deleting:j()})],b),i(de,d),ke()}function _e(de,S){Se(S,!1);const[q,H]=Ve(),R=()=>Ke(j,"$sharedWebviewStore",q),M=O(),c=O(),X=O();let F=x(S,"agent",8),I=x(S,"selected",8,!1),B=x(S,"onSelect",8);const T=Ee(Te.key),j=Ee(Be);let v=O(!1),E=O(null),G=null;function d(){A(E,null),G&&(clearTimeout(G),G=null)}async function ce(){return!!e(X)&&await(async g=>await T.sshToRemoteAgent(g.remote_agent_id))(F())}V(()=>R(),()=>{var g;A(M,((g=R().state)==null?void 0:g.pinnedAgents)||{})}),V(()=>(e(M),y(F())),()=>{var g;A(c,((g=e(M))==null?void 0:g[F().remote_agent_id])===!0)}),V(()=>(y(F()),Y),()=>{A(X,F().status===Y.agentRunning||F().status===Y.agentIdle)}),Ce(),ye();const me=b(()=>({onSSH:ce,canSSH:e(X)}));Ct(de,{get agent(){return F()},get selected(){return I()},get isPinned(){return e(c)},get onSelect(){return B()},onDelete:()=>async function(g){var $,ne;d(),A(v,!0);const Z=(($=R().state)==null?void 0:$.agentOverviews)||[];try{if(!await T.deleteRemoteAgent(g))throw new Error("Failed to delete agent");if(j.update(k=>{if(k)return{...k,agentOverviews:k.agentOverviews.filter(s=>s.remote_agent_id!==g)}}),(((ne=R().state)==null?void 0:ne.pinnedAgents)||{})[g])try{await T.deletePinnedAgentFromStore(g);const k=await T.getPinnedAgentsFromStore();j.update(s=>{if(s)return{...s,pinnedAgents:k}})}catch(k){console.error("Failed to remove pinned status:",k)}}catch(k){console.error("Failed to delete agent:",k),j.update(s=>{if(s)return{...s,agentOverviews:Z}}),A(E,k instanceof Error?k.message:"Failed to delete agent"),G=setTimeout(()=>{d()},5e3)}finally{A(v,!1)}}(F().remote_agent_id),onTogglePinned:()=>async function(g){try{e(c)?await T.deletePinnedAgentFromStore(g):await T.savePinnedAgentToStore(g,!0);const Z=await T.getPinnedAgentsFromStore();j.update($=>{if($)return{...$,pinnedAgents:Z}})}catch(Z){console.error("Failed to toggle pinned status:",Z)}}(F().remote_agent_id),get sshConfig(){return e(me)},get deletionError(){return e(E)},set deletionError(g){A(E,g)},get isDeleting(){return e(v)},set isDeleting(g){A(v,g)},$$legacy:!0}),ke(),H()}var Et=h('<div class="section-header svelte-1tegnqi"><!></div>');function $e(de,S){let q=x(S,"title",8);var H=Et(),R=_(H);se(R,{size:2,color:"secondary",children:(M,c)=>{var X=ae();J(()=>le(X,q())),i(M,X)},$$slots:{default:!0}}),i(de,H)}var Dt=h('<div class="empty-state svelte-aiqmvp"><div class="l-loading-container svelte-aiqmvp"><!> <!></div></div>'),Lt=h('<div class="empty-state svelte-aiqmvp"><!></div>'),Wt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Mt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Bt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),jt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Gt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Qt=h('<!> <div class="agent-grid svelte-aiqmvp"></div>',1),Ut=h("<!> <!> <!> <!> <!> <!>",1),Jt=h('<div class="agent-list svelte-aiqmvp"><!></div>'),Kt=h('<div class="l-main svelte-1941nw6"><h1 class="l-main__title svelte-1941nw6"><span class="l-main__title-logo svelte-1941nw6"><!></span> Remote Agents</h1> <!></div>');rt(function(de,S){Se(S,!1);const q=new dt(lt),H=new ut(q,void 0,mt,pt);q.registerConsumer(H),Qe(Be,H);const R=new Te(q);Qe(Te.key,R),at(()=>(H.fetchStateFromExtension().then(()=>{H.update(I=>{if(!I)return;const B=[...I.activeWebviews,"home"];return I.pinnedAgents?{...I,activeWebviews:B}:{...I,activeWebviews:B,pinnedAgents:{}}})}),()=>{q.dispose(),R.dispose()})),ye();var M=Kt();Je("message",nt,function(...I){var B;(B=q.onMessageFromExtension)==null||B.apply(this,I)});var c=_(M),X=_(c),F=_(X);yt(F),function(I,B){Se(B,!1);const[T,j]=Ve(),v=()=>Ke(ce,"$sharedWebviewStore",T),E=O(),G=O(),d=O(),ce=Ee(Be),me=Ee(Te.key);function g(s){ce.update(l=>{if(l)return{...l,selectedAgentId:s}})}V(()=>v(),()=>{var s;A(E,wt(((s=v().state)==null?void 0:s.agentOverviews)||[]))}),V(()=>v(),()=>{var s;A(G,((s=v().state)==null?void 0:s.pinnedAgents)||{})}),V(()=>(e(E),e(G),Oe),()=>{A(d,e(E).reduce((s,l)=>{var Q;return((Q=e(G))==null?void 0:Q[l.remote_agent_id])===!0?s.pinned.push(l):l.status===Y.agentIdle&&l.has_updates?s.readyToReview.push(l):l.status===Y.agentRunning||l.status===Y.agentStarting||l.workspace_status===Oe.workspaceResuming?s.running.push(l):l.status===Y.agentFailed?s.failed.push(l):l.status===Y.agentIdle||l.workspace_status===Oe.workspacePaused||l.workspace_status===Oe.workspacePausing?s.idle.push(l):s.additional.push(l),s},{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]}))}),V(()=>v(),()=>{var s;(s=v().state)!=null&&s.agentOverviews||me.focusAugmentPanel()}),Ce(),ye();var Z=Jt(),$=_(Z),ne=s=>{var l=Dt(),Q=_(l),ee=_(Q);st(ee,{});var D=p(ee,2);se(D,{size:3,color:"secondary",children:(L,ve)=>{var re=ae("Loading the Augment panel...");i(L,re)},$$slots:{default:!0}}),i(s,l)},k=(s,l)=>{var Q=D=>{var L=Lt(),ve=_(L);se(ve,{size:3,color:"secondary",children:(re,xe)=>{var be=ae("No agents available");i(re,be)},$$slots:{default:!0}}),i(D,L)},ee=D=>{var L=Ut(),ve=K(L),re=w=>{var a=Wt(),r=K(a);$e(r,{title:"Pinned"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).pinned)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};P(ve,w=>{e(d),o(()=>e(d).pinned.length>0)&&w(re)});var xe=p(ve,2),be=w=>{var a=Mt(),r=K(a);$e(r,{title:"Ready to review"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).readyToReview)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};P(xe,w=>{e(d),o(()=>e(d).readyToReview.length>0)&&w(be)});var Ae=p(xe,2),ze=w=>{var a=Bt(),r=K(a);$e(r,{title:"Running agents"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).running)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};P(Ae,w=>{e(d),o(()=>e(d).running.length>0)&&w(ze)});var He=p(Ae,2),Fe=w=>{var a=jt(),r=K(a);$e(r,{title:"Idle agents"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).idle)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};P(He,w=>{e(d),o(()=>e(d).idle.length>0)&&w(Fe)});var Ie=p(He,2),De=w=>{var a=Gt(),r=K(a);$e(r,{title:"Failed agents"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).failed)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};P(Ie,w=>{e(d),o(()=>e(d).failed.length>0)&&w(De)});var Le=p(Ie,2),We=w=>{var a=Qt(),r=K(a);$e(r,{title:"Other agents"});var f=p(r,2);ue(f,7,()=>(e(d),o(()=>e(d).additional)),(n,t)=>n.remote_agent_id+t,(n,t)=>{const m=b(()=>(e(t),v(),o(()=>{var u;return e(t).remote_agent_id===((u=v().state)==null?void 0:u.selectedAgentId)})));_e(n,{get agent(){return e(t)},get selected(){return e(m)},onSelect:g})}),i(w,a)};P(Le,w=>{e(d),o(()=>e(d).additional.length>0)&&w(We)}),i(D,L)};P(s,D=>{v(),o(()=>{var L;return((L=v().state)==null?void 0:L.agentOverviews.length)===0})?D(Q):D(ee,!1)},l)};P($,s=>{v(),o(()=>{var l;return!((l=v().state)!=null&&l.agentOverviews)})?s(ne):s(k,!1)}),i(I,Z),ke(),j()}(p(c,2),{}),i(de,M),ke()},{target:document.getElementById("app")});
