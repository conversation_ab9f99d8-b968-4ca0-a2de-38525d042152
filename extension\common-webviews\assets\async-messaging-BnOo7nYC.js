var M=Object.defineProperty;var p=(a,e,s)=>e in a?M(a,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[e]=s;var n=(a,e,s)=>p(a,typeof e!="symbol"?e+"":e,s);import{W as h}from"./IconButtonAugment-B8y0FMb_.js";class I{constructor(e,s=1e3){n(this,"_idToPromiseFns",new Map);n(this,"registerPromiseContext",e=>new Promise((s,t)=>{this._idToPromiseFns.set(e.requestId,{resolve:s,reject:t})}));n(this,"resolveAsyncMsg",e=>{if(e.type!==h.asyncWrapper)return!1;const s=e,t=this._idToPromiseFns.get(s.requestId);return!!t&&(this._idToPromiseFns.delete(s.requestId),s.error?t.reject(new Error(s.error)):t.resolve(s),!0)});n(this,"rejectAsyncMsg",(e,s)=>{const t=this._idToPromiseFns.get(e.requestId);t&&(this._idToPromiseFns.delete(e.requestId),console.debug(`AsyncMsgSender: Rejecting request, reason: ${s}`,e),t.reject(s))});n(this,"sendOrTimeout",(e,s=this._timeoutMs)=>{this._postMsgFn(e),s>0&&setTimeout(()=>{var t;return this.rejectAsyncMsg(e,new m("MessageTimeout",`Request timed out: ${(t=e==null?void 0:e.baseMsg)==null?void 0:t.type}, id: ${e==null?void 0:e.requestId}`))},s)});n(this,"send",async(e,s=this._timeoutMs)=>{const t=g(e),i=this.registerPromiseContext(t);this.sendOrTimeout(t,s);const o=await i;if(o.error)throw new Error(o.error);if(!o.baseMsg)throw new m("InvalidResponse","No response or error message");return o.baseMsg});n(this,"sendToSidecar",async(e,s=this._timeoutMs)=>{const t=g(e,"sidecar"),i=this.registerPromiseContext(t);this.sendOrTimeout(t,s);const o=await i;if(o.error)throw new Error(o.error);if(!o.baseMsg)throw new m("InvalidResponse","No response or error message");return o.baseMsg});this._postMsgFn=e,this._timeoutMs=s,window.addEventListener("message",t=>{this.resolveAsyncMsg(t.data)})}async*stream(e,s=this._timeoutMs,t=this._timeoutMs){let i=g(e);i.streamCtx={streamMsgIdx:0,streamNextRequestId:""};let o=0,c=!1;try{let d=this.registerPromiseContext(i);this.sendOrTimeout(i,s);const w=new Promise((r,u)=>{t<=0||setTimeout(()=>u(new m("StreamTimeout","Stream timed out")),t)});for(;!c;){const r=await Promise.race([d,w]);if((r==null?void 0:r.type)!==h.asyncWrapper)throw new m("InvalidResponse",`Received unexpected message: ${r}`);if(r.error)throw new Error(r.error);if(!r.streamCtx||r.streamCtx.isStreamComplete)return;if(!r.baseMsg)throw new m("InvalidResponse","No response or error message");if(r.streamCtx.streamMsgIdx!==o){const u=r.streamCtx.streamMsgIdx;throw new Error(`Received out of order stream chunk. Expected ${o} but got ${u}`)}o=r.streamCtx.streamMsgIdx+1,i={...i,streamCtx:{streamMsgIdx:o,streamNextRequestId:""},requestId:r.streamCtx.streamNextRequestId},d=this.registerPromiseContext(i),yield r.baseMsg}}finally{if(!c){c=!0;try{this._idToPromiseFns.delete(i.requestId)}catch(d){console.warn("Error sending stream cancellation message:",d)}}}}}function g(a,e="host"){return{type:h.asyncWrapper,requestId:crypto.randomUUID(),error:null,baseMsg:a,destination:e}}class m extends Error{constructor(s,t){super();n(this,"name");n(this,"message");this.name=s,this.message=t}}export{I as A,m as a};
