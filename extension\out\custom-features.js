const _0x3eb09c = _0x2025;
(function (_0x4c4d43, _0xf53a7d) {
  const _0x42da09 = _0x2025,
    _0x3c704b = _0x4c4d43();
  while (!![]) {
    try {
      const _0x4d1506 = -parseInt(_0x42da09(0x1d9)) / 0x1 * (-parseInt(_0x42da09(0x178)) / 0x2) + parseInt(_0x42da09(0x1d8)) / 0x3 + -parseInt(_0x42da09(0x179)) / 0x4 + -parseInt(_0x42da09(0x19f)) / 0x5 + -parseInt(_0x42da09(0x1f2)) / 0x6 + parseInt(_0x42da09(0x1d1)) / 0x7 + parseInt(_0x42da09(0x1f3)) / 0x8;
      if (_0x4d1506 === _0xf53a7d) break;
      else _0x3c704b['push'](_0x3c704b['shift']());
    } catch (_0x24c5d4) {
      _0x3c704b['push'](_0x3c704b['shift']());
    }
  }
}(_0x24f5, 0x47d2b));
const vscode = require(_0x3eb09c(0x1b5)),
  crypto = require(_0x3eb09c(0x177));

function _0x24f5() {
  const _0x4e0a55 = ['sessionId更新成功！新值:\x20', 'update', '1228122IgMkzS', '6419688xuHHkF', 'forEach', 'showInformationMessage', '存储更新后的会话数据失败', 'error', 'email', '当前:\x20', 'context', '显示当前存储的认证信息，支持复制和查看完整数据', 'augment.sessions', '选择要执行的操作', '输入\x20tenantURL', '\x20个自定义命令', 'handleUpdateMachineCode', 'crypto', '49526rVWvMS', '1590940iZniFb', '⚡\x20开始注册自定义命令...', 'updateAccessToken', 'stack', '修改\x20accessToken\x20或\x20tenantURL', 'json', 'subscriptions', 'accessToken\x20已复制到剪贴板', 'isInitialized', '获取\x20accessToken', '📋\x20错误类型:', 'handleSetToken', '查看当前的\x20accessToken\x20和\x20tenantURL', '获取\x20accessToken\x20失败:\x20', 'push', '输入新的\x20accessToken...', 'Custom\x20features\x20disposed', '⚠️\x20自定义功能已经初始化过了，跳过重复初始化', 'commands', 'accessToken\x20不能为空', 'handleNewPool', 'updateSessionsData', 'Failed\x20to\x20get\x20current\x20accessToken\x20for\x20placeholder:', '📊\x20当前订阅数量:\x20', '未设置', '获取访问令牌失败，重试\x20', '🚀\x20==========\x20自定义功能详细初始化开始\x20==========', 'label', 'workbench.action.reloadWindow', 'accessToken', 'handler', '💾\x20保存上下文和扩展引用...', 'success', 'augmentExtension', '请输入有效的URL\x20(例如:\x20https://d5.api.augmentcode.com/)', '\x20个自定义命令:', '\x20\x20\x20✅\x20', 'info', '1695435BBmWFD', 'showTextDocument', '复制\x20accessToken', 'exports', '未提供', 'then', 'get', 'handleGetAccessToken', 'tenantURL', 'string', '显示完整数据', 'warn', 'window', 'Secret\x20', 'Sessions\x20data\x20updated\x20successfully', 'openTextDocument', '仅更新\x20accessToken', '已提供', 'logger', '只更新\x20augment.sessions\x20中的\x20accessToken', 'showQuickPick', 'https://d5.api.augmentcode.com/', 'vscode', 'accessToken\x20更新成功！', '📊\x20错误堆栈:', '显示更新后的数据', 'secrets', 'augment.custom.newpool', 'stringify', '\x20stored\x20successfully', 'env', '重置设备唯一标识符', 'parse', 'trim', '🎉\x20==========\x20自定义功能初始化完成\x20==========', '...', '❌\x20错误信息:', 'Failed\x20to\x20get\x20access\x20token\x20after\x20retries:', '更新会话数据失败', 'accessToken\x20长度似乎太短', '更新会话数据', 'createLogger', 'AccessToken\x20updated\x20successfully', 'registerCommand', 'getAccessToken', 'tenantURL\x20不能为空', 'scopes', '更新机器码', 'showInputBox', '更新\x20accessToken\x20失败:\x20', '2698507NPYqlc', '✅\x20自定义功能初始化成功', 'substring', 'workspace', 'setSecret', '解析会话数据失败', '快速更新：仅修改\x20accessToken，保留\x20tenantURL\x20和权限范围', '68913bcEqCG', '1IlXoII', 'showErrorMessage', '更新\x20augment.sessions\x20中的\x20tenantURL\x20和\x20accessToken', 'Failed\x20to\x20parse\x20existing\x20sessions\x20data,\x20creating\x20new\x20object', 'clipboard', '会话数据更新成功！', 'Failed\x20to\x20parse\x20sessions\x20data:', 'name', '更新认证信息，支持仅更新\x20accessToken\x20或完整更新会话数据', 'accessToken:\x20', '\x0atenantURL:\x20', 'extensionPath', '重载窗口', 'message', '设置\x20accessToken', 'data', 'randomUUID', 'Failed\x20to\x20get\x20current\x20sessions\x20data:', 'length', '输入\x20accessToken', '未找到会话数据', '📋\x20准备注册\x20', 'debug'];
  _0x24f5 = function () {
    return _0x4e0a55;
  };
  return _0x24f5();
}
class AugmentCustomFeatures {
  constructor() {
    const _0x1dde60 = _0x3eb09c;
    this[_0x1dde60(0x1b1)] = this[_0x1dde60(0x1c8)](), this['isInitialized'] = ![];
  } [_0x3eb09c(0x1c8)]() {
    return {
      'info': (_0x596bff, ..._0x1eb8ae) => { },
      'warn': (_0x48ec0f, ..._0x164c77) => { },
      'error': (_0x1d299f, ..._0x12f122) => { },
      'debug': (_0x3ce353, ..._0x455b78) => { }
    };
  }
  async ['initialize'](_0x1ea6d1, _0x32c0e4 = null) {
    const _0x13c186 = _0x3eb09c;
    if (this[_0x13c186(0x181)]) {
      this[_0x13c186(0x1b1)][_0x13c186(0x1aa)](_0x13c186(0x18a));
      return;
    }
    try {
      this[_0x13c186(0x1b1)]['info'](_0x13c186(0x193)), this['logger'][_0x13c186(0x19e)]('📋\x20上下文信息:'), this[_0x13c186(0x1b1)]['info']('\x20\x20\x20-\x20扩展路径:', _0x1ea6d1[_0x13c186(0x1e4)]), this['logger'][_0x13c186(0x19e)]('\x20\x20\x20-\x20订阅数量:', _0x1ea6d1['subscriptions']['length']), this[_0x13c186(0x1b1)][_0x13c186(0x19e)]('\x20\x20\x20-\x20Augment扩展实例:', _0x32c0e4 ? _0x13c186(0x1b0) : _0x13c186(0x1a3)), this[_0x13c186(0x1b1)][_0x13c186(0x19e)](_0x13c186(0x198)), this['context'] = _0x1ea6d1, this[_0x13c186(0x19a)] = _0x32c0e4, this[_0x13c186(0x1b1)][_0x13c186(0x19e)](_0x13c186(0x17a)), this['registerCommands'](), this['isInitialized'] = !![], this[_0x13c186(0x1b1)]['info'](_0x13c186(0x1d2)), this[_0x13c186(0x1b1)][_0x13c186(0x19e)](_0x13c186(0x1c1));
    } catch (_0x219634) {
      this['logger'][_0x13c186(0x1f7)]('💥\x20==========\x20自定义功能初始化失败\x20=========='), this[_0x13c186(0x1b1)]['error'](_0x13c186(0x1c3), _0x219634['message']), this['logger'][_0x13c186(0x1f7)](_0x13c186(0x183), _0x219634[_0x13c186(0x1e0)]), this['logger'][_0x13c186(0x1f7)](_0x13c186(0x1b7), _0x219634[_0x13c186(0x17c)]);
      throw _0x219634;
    }
  } ['registerCommands']() {
    const _0x1b90e3 = _0x3eb09c;
    this[_0x1b90e3(0x1b1)][_0x1b90e3(0x19e)]('📝\x20开始注册自定义命令...');
    const _0x51f4cb = [{
      'id': _0x1b90e3(0x1ba),
      'handler': () => this[_0x1b90e3(0x18d)]()
    }];
    this[_0x1b90e3(0x1b1)][_0x1b90e3(0x19e)](_0x1b90e3(0x1ee) + _0x51f4cb[_0x1b90e3(0x1eb)] + _0x1b90e3(0x19c)), _0x51f4cb[_0x1b90e3(0x1f4)]((_0x171f99, _0x275ae3) => {
      const _0x2586f7 = _0x1b90e3;
      this[_0x2586f7(0x1b1)][_0x2586f7(0x19e)]('\x20\x20\x20' + (_0x275ae3 + 0x1) + '.\x20' + _0x171f99['id']);
      const _0x1823ad = vscode[_0x2586f7(0x18b)][_0x2586f7(0x1ca)](_0x171f99['id'], _0x171f99[_0x2586f7(0x197)]);
      this['context'][_0x2586f7(0x17f)][_0x2586f7(0x187)](_0x1823ad), this['logger'][_0x2586f7(0x19e)](_0x2586f7(0x19d) + _0x171f99['id'] + '\x20注册成功');
    }), this['logger']['info']('🎉\x20成功注册了\x20' + _0x51f4cb['length'] + _0x1b90e3(0x175)), this[_0x1b90e3(0x1b1)]['info'](_0x1b90e3(0x190) + this[_0x1b90e3(0x170)][_0x1b90e3(0x17f)]['length']);
  }
  async [_0x3eb09c(0x1cb)]() {
    const _0x39bbc4 = _0x3eb09c;
    try {
      let _0x3490a3 = 0x0;
      const _0x3c6e07 = 0x3;
      while (_0x3490a3 < _0x3c6e07) {
        try {
          const _0x2e4f2a = await this[_0x39bbc4(0x170)][_0x39bbc4(0x1b9)][_0x39bbc4(0x1a5)]('augment.sessions');
          if (_0x2e4f2a) try {
            const _0x268fe4 = JSON[_0x39bbc4(0x1bf)](_0x2e4f2a);
            return {
              'success': !![],
              'accessToken': _0x268fe4[_0x39bbc4(0x196)],
              'tenantURL': _0x268fe4[_0x39bbc4(0x1a7)],
              'data': _0x268fe4
            };
          } catch (_0x170de1) {
            return this[_0x39bbc4(0x1b1)]['error'](_0x39bbc4(0x1df), _0x170de1), {
              'success': ![],
              'error': _0x39bbc4(0x1d6)
            };
          } else return {
            'success': ![],
            'error': _0x39bbc4(0x1ed)
          };
        } catch (_0x23a6c4) {
          _0x3490a3++, this[_0x39bbc4(0x1b1)]['warn'](_0x39bbc4(0x192) + _0x3490a3 + '/' + _0x3c6e07 + ':', _0x23a6c4[_0x39bbc4(0x1e6)]);
          if (_0x3490a3 >= _0x3c6e07) throw _0x23a6c4;
          await new Promise(_0x22eeb4 => setTimeout(_0x22eeb4, 0x3e8 * _0x3490a3));
        }
      }
    } catch (_0x4deb88) {
      return this[_0x39bbc4(0x1b1)][_0x39bbc4(0x1f7)](_0x39bbc4(0x1c4), _0x4deb88), {
        'success': ![],
        'error': _0x4deb88[_0x39bbc4(0x1e6)]
      };
    }
  }
  async ['setSecret'](_0x593348, _0x8dbdc2) {
    const _0x467d29 = _0x3eb09c;
    try {
      const _0x557dfb = typeof _0x8dbdc2 === _0x467d29(0x1a8) ? _0x8dbdc2 : JSON[_0x467d29(0x1bb)](_0x8dbdc2);
      return await this['context'][_0x467d29(0x1b9)]['store'](_0x593348, _0x557dfb), this['logger']['info'](_0x467d29(0x1ac) + _0x593348 + _0x467d29(0x1bc)), !![];
    } catch (_0x59373d) {
      return this[_0x467d29(0x1b1)][_0x467d29(0x1f7)]('Failed\x20to\x20store\x20secret\x20' + _0x593348 + ':', _0x59373d), ![];
    }
  }
  async [_0x3eb09c(0x17b)](_0x2e9a7a) {
    const _0x3898fe = _0x3eb09c;
    try {
      const _0xfcaf59 = await this[_0x3898fe(0x170)]['secrets']['get'](_0x3898fe(0x172));
      let _0x4f9680 = {};
      if (_0xfcaf59) try {
        _0x4f9680 = JSON[_0x3898fe(0x1bf)](_0xfcaf59);
      } catch (_0x39aebd) {
        this['logger']['warn'](_0x3898fe(0x1dc)), _0x4f9680 = {};
      }
      _0x4f9680['accessToken'] = _0x2e9a7a;
      !_0x4f9680['tenantURL'] && (_0x4f9680[_0x3898fe(0x1a7)] = _0x3898fe(0x1b4));
      !_0x4f9680['scopes'] && (_0x4f9680[_0x3898fe(0x1cd)] = [_0x3898fe(0x1f8)]);
      const _0xaa4a6c = await this[_0x3898fe(0x1d5)](_0x3898fe(0x172), _0x4f9680);
      return _0xaa4a6c ? (this[_0x3898fe(0x1b1)][_0x3898fe(0x19e)](_0x3898fe(0x1c9)), {
        'success': !![],
        'data': _0x4f9680
      }) : {
        'success': ![],
        'error': _0x3898fe(0x1f6)
      };
    } catch (_0x549911) {
      return this[_0x3898fe(0x1b1)][_0x3898fe(0x1f7)]('Failed\x20to\x20update\x20access\x20token:', _0x549911), {
        'success': ![],
        'error': _0x549911[_0x3898fe(0x1e6)]
      };
    }
  }
  async [_0x3eb09c(0x18e)](_0x105732, _0x3e7091) {
    const _0x50d48e = _0x3eb09c;
    try {
      const _0x54f108 = await this[_0x50d48e(0x170)][_0x50d48e(0x1b9)]['get'](_0x50d48e(0x172));
      let _0x4e90e5 = {};
      if (_0x54f108) try {
        _0x4e90e5 = JSON[_0x50d48e(0x1bf)](_0x54f108);
      } catch (_0x3bca0b) {
        this['logger'][_0x50d48e(0x1aa)](_0x50d48e(0x1dc)), _0x4e90e5 = {};
      }
      _0x4e90e5[_0x50d48e(0x1a7)] = _0x105732, _0x4e90e5['accessToken'] = _0x3e7091;
      !_0x4e90e5[_0x50d48e(0x1cd)] && (_0x4e90e5[_0x50d48e(0x1cd)] = [_0x50d48e(0x1f8)]);
      const _0xb79b28 = await this['setSecret']('augment.sessions', _0x4e90e5);
      return _0xb79b28 ? (this['logger'][_0x50d48e(0x19e)](_0x50d48e(0x1ad)), {
        'success': !![],
        'data': _0x4e90e5
      }) : {
        'success': ![],
        'error': _0x50d48e(0x1f6)
      };
    } catch (_0x147b6e) {
      return this['logger'][_0x50d48e(0x1f7)]('Failed\x20to\x20update\x20sessions\x20data:', _0x147b6e), {
        'success': ![],
        'error': _0x147b6e['message']
      };
    }
  }
  async [_0x3eb09c(0x18d)]() {
    const _0x6c28f8 = _0x3eb09c;
    try {
      const _0x20b8ae = await vscode['window'][_0x6c28f8(0x1b3)]([{
        'label': _0x6c28f8(0x182),
        'description': _0x6c28f8(0x185),
        'detail': _0x6c28f8(0x171)
      }, {
        'label': '设置\x20accessToken',
        'description': _0x6c28f8(0x17d),
        'detail': _0x6c28f8(0x1e1)
      }, {
        'label': _0x6c28f8(0x1ce),
        'description': _0x6c28f8(0x1be),
        'detail': '生成并更新当前设备的机器码标识'
      }], {
        'placeHolder': _0x6c28f8(0x173)
      });
      if (!_0x20b8ae) return;
      if (_0x20b8ae[_0x6c28f8(0x194)] === '获取\x20accessToken') await this[_0x6c28f8(0x1a6)]();
      else {
        if (_0x20b8ae['label'] === _0x6c28f8(0x1e7)) await this[_0x6c28f8(0x184)]();
        else _0x20b8ae[_0x6c28f8(0x194)] === _0x6c28f8(0x1ce) && await this[_0x6c28f8(0x176)]();
      }
    } catch (_0x505a62) {
      vscode[_0x6c28f8(0x1ab)]['showErrorMessage']('错误:\x20' + _0x505a62['message']);
    }
  }
  async [_0x3eb09c(0x1a6)]() {
    const _0x5cf219 = _0x3eb09c;
    try {
      const _0x293d97 = await this[_0x5cf219(0x1cb)]();
      if (_0x293d97[_0x5cf219(0x199)]) {
        const _0x56c6f2 = _0x293d97[_0x5cf219(0x196)] && _0x293d97[_0x5cf219(0x196)][_0x5cf219(0x1eb)] > 0x10 ? _0x293d97['accessToken']['substring'](0x0, 0x8) + '...' + _0x293d97[_0x5cf219(0x196)][_0x5cf219(0x1d3)](_0x293d97[_0x5cf219(0x196)][_0x5cf219(0x1eb)] - 0x8) : _0x293d97[_0x5cf219(0x196)] || _0x5cf219(0x191),
          _0x1b3a5f = _0x5cf219(0x1e2) + _0x56c6f2 + _0x5cf219(0x1e3) + (_0x293d97[_0x5cf219(0x1a7)] || '未设置'),
          _0x731faa = await vscode['window'][_0x5cf219(0x1f5)](_0x1b3a5f, _0x5cf219(0x1a1), _0x5cf219(0x1a9));
        if (_0x731faa === _0x5cf219(0x1a1) && _0x293d97[_0x5cf219(0x196)]) await vscode[_0x5cf219(0x1bd)][_0x5cf219(0x1dd)]['writeText'](_0x293d97['accessToken']), vscode[_0x5cf219(0x1ab)][_0x5cf219(0x1f5)](_0x5cf219(0x180));
        else {
          if (_0x731faa === _0x5cf219(0x1a9)) {
            const _0x398b61 = await vscode[_0x5cf219(0x1d4)]['openTextDocument']({
              'content': JSON['stringify'](_0x293d97['data'], null, 0x2),
              'language': _0x5cf219(0x17e)
            });
            await vscode['window'][_0x5cf219(0x1a0)](_0x398b61);
          }
        }
      } else vscode['window'][_0x5cf219(0x1da)](_0x5cf219(0x186) + _0x293d97[_0x5cf219(0x1f7)]);
    } catch (_0x24808d) {
      vscode[_0x5cf219(0x1ab)][_0x5cf219(0x1da)]('错误:\x20' + _0x24808d[_0x5cf219(0x1e6)]);
    }
  }
  async [_0x3eb09c(0x184)]() {
    const _0x2b58da = _0x3eb09c;
    try {
      const _0x2a70ae = await vscode[_0x2b58da(0x1ab)][_0x2b58da(0x1b3)]([{
        'label': _0x2b58da(0x1af),
        'description': _0x2b58da(0x1b2),
        'detail': _0x2b58da(0x1d7)
      }, {
        'label': _0x2b58da(0x1c7),
        'description': _0x2b58da(0x1db),
        'detail': '完整更新：通过引导输入同时修改\x20tenantURL\x20和\x20accessToken'
      }], {
        'placeHolder': '选择要更新的内容'
      });
      if (!_0x2a70ae) return;
      if (_0x2a70ae[_0x2b58da(0x194)] === _0x2b58da(0x1af)) {
        let _0x361dec = _0x2b58da(0x188);
        try {
          const _0x10fbd6 = await this[_0x2b58da(0x170)][_0x2b58da(0x1b9)][_0x2b58da(0x1a5)]('augment.sessions');
          if (_0x10fbd6) {
            const _0x1ca629 = JSON[_0x2b58da(0x1bf)](_0x10fbd6);
            if (_0x1ca629[_0x2b58da(0x196)]) {
              const _0x1d6b52 = _0x1ca629[_0x2b58da(0x196)];
              _0x1d6b52['length'] > 0x10 ? _0x361dec = _0x2b58da(0x1f9) + _0x1d6b52['substring'](0x0, 0x8) + _0x2b58da(0x1c2) + _0x1d6b52[_0x2b58da(0x1d3)](_0x1d6b52[_0x2b58da(0x1eb)] - 0x8) : _0x361dec = _0x2b58da(0x1f9) + _0x1d6b52;
            }
          }
        } catch (_0x35a080) {
          this[_0x2b58da(0x1b1)][_0x2b58da(0x1ef)](_0x2b58da(0x18f), _0x35a080);
        }
        const _0x4267fc = await vscode['window'][_0x2b58da(0x1cf)]({
          'prompt': '输入新的\x20accessToken',
          'placeHolder': _0x361dec,
          'password': !![],
          'validateInput': _0x2c5207 => {
            const _0x3cfd2c = _0x2b58da;
            if (!_0x2c5207 || _0x2c5207[_0x3cfd2c(0x1c0)]()[_0x3cfd2c(0x1eb)] === 0x0) return 'accessToken\x20不能为空';
            if (_0x2c5207['length'] < 0xa) return _0x3cfd2c(0x1c6);
            return null;
          }
        });
        if (!_0x4267fc) return;
        const _0x3c3458 = await this['updateAccessToken'](_0x4267fc[_0x2b58da(0x1c0)]());
        if (_0x3c3458[_0x2b58da(0x199)]) {
          vscode['window'][_0x2b58da(0x1f5)](_0x2b58da(0x1b6));
          const _0x5b4470 = await vscode[_0x2b58da(0x1ab)]['showInformationMessage'](_0x2b58da(0x1b6), '显示更新后的数据');
          if (_0x5b4470 === _0x2b58da(0x1b8)) {
            const _0x2007f7 = await vscode[_0x2b58da(0x1d4)]['openTextDocument']({
              'content': JSON[_0x2b58da(0x1bb)](_0x3c3458[_0x2b58da(0x1e8)], null, 0x2),
              'language': _0x2b58da(0x17e)
            });
            await vscode[_0x2b58da(0x1ab)][_0x2b58da(0x1a0)](_0x2007f7);
          }
        } else vscode[_0x2b58da(0x1ab)][_0x2b58da(0x1da)](_0x2b58da(0x1d0) + _0x3c3458[_0x2b58da(0x1f7)]);
      } else {
        let _0x3164fd = {
          'accessToken': '',
          'tenantURL': _0x2b58da(0x1b4),
          'scopes': [_0x2b58da(0x1f8)]
        };
        try {
          const _0x524695 = await this[_0x2b58da(0x170)][_0x2b58da(0x1b9)]['get'](_0x2b58da(0x172));
          if (_0x524695) {
            const _0x712c1d = JSON[_0x2b58da(0x1bf)](_0x524695);
            _0x3164fd = {
              ..._0x3164fd,
              ..._0x712c1d
            };
          }
        } catch (_0x5f5645) {
          this[_0x2b58da(0x1b1)][_0x2b58da(0x1ef)](_0x2b58da(0x1ea), _0x5f5645);
        }
        const _0xcbe186 = await vscode[_0x2b58da(0x1ab)][_0x2b58da(0x1cf)]({
          'prompt': _0x2b58da(0x174),
          'placeHolder': _0x2b58da(0x1f9) + _0x3164fd[_0x2b58da(0x1a7)],
          'value': _0x3164fd[_0x2b58da(0x1a7)],
          'validateInput': _0x4eec66 => {
            const _0x143461 = _0x2b58da;
            if (!_0x4eec66 || _0x4eec66['trim']()[_0x143461(0x1eb)] === 0x0) return _0x143461(0x1cc);
            try {
              return new URL(_0x4eec66), null;
            } catch {
              return _0x143461(0x19b);
            }
          }
        });
        if (!_0xcbe186) return;
        const _0x17fa24 = _0x3164fd['accessToken']['length'] > 0x10 ? _0x3164fd['accessToken']['substring'](0x0, 0x8) + _0x2b58da(0x1c2) + _0x3164fd[_0x2b58da(0x196)][_0x2b58da(0x1d3)](_0x3164fd[_0x2b58da(0x196)][_0x2b58da(0x1eb)] - 0x8) : _0x3164fd['accessToken'],
          _0x45683c = await vscode[_0x2b58da(0x1ab)]['showInputBox']({
            'prompt': _0x2b58da(0x1ec),
            'placeHolder': _0x2b58da(0x1f9) + _0x17fa24,
            'password': !![],
            'validateInput': _0x19c82c => {
              const _0x1651a0 = _0x2b58da;
              if (!_0x19c82c || _0x19c82c[_0x1651a0(0x1c0)]()[_0x1651a0(0x1eb)] === 0x0) return _0x1651a0(0x18c);
              if (_0x19c82c[_0x1651a0(0x1eb)] < 0xa) return _0x1651a0(0x1c6);
              return null;
            }
          });
        if (!_0x45683c) return;
        const _0x40ba3c = {
          ..._0x3164fd,
          'tenantURL': _0xcbe186[_0x2b58da(0x1c0)](),
          'accessToken': _0x45683c['trim']()
        },
          _0x20ce96 = await this[_0x2b58da(0x1d5)](_0x2b58da(0x172), _0x40ba3c);
        if (_0x20ce96) {
          vscode[_0x2b58da(0x1ab)]['showInformationMessage'](_0x2b58da(0x1de));
          const _0x54e07f = await vscode[_0x2b58da(0x1ab)][_0x2b58da(0x1f5)]('会话数据更新成功！', _0x2b58da(0x1b8));
          if (_0x54e07f === '显示更新后的数据') {
            const _0x58b085 = await vscode[_0x2b58da(0x1d4)][_0x2b58da(0x1ae)]({
              'content': JSON[_0x2b58da(0x1bb)](_0x40ba3c, null, 0x2),
              'language': _0x2b58da(0x17e)
            });
            await vscode[_0x2b58da(0x1ab)][_0x2b58da(0x1a0)](_0x58b085);
          }
        } else vscode[_0x2b58da(0x1ab)]['showErrorMessage'](_0x2b58da(0x1c5));
      }
    } catch (_0x79014a) {
      vscode[_0x2b58da(0x1ab)]['showErrorMessage']('错误:\x20' + _0x79014a['message']);
    }
  }
  async [_0x3eb09c(0x176)]() {
    const _0x6993cd = _0x3eb09c;
    try {
      const _0x477189 = crypto[_0x6993cd(0x1e9)]();
      await this[_0x6993cd(0x170)]['globalState'][_0x6993cd(0x1f1)]('sessionId', _0x477189), vscode[_0x6993cd(0x1ab)][_0x6993cd(0x1f5)](_0x6993cd(0x1f0) + _0x477189 + '，请重载窗口以生效', '重载窗口')[_0x6993cd(0x1a4)](_0x2f5707 => {
        const _0xc64b19 = _0x6993cd;
        _0x2f5707 === _0xc64b19(0x1e5) && vscode['commands']['executeCommand'](_0xc64b19(0x195));
      });
    } catch (_0x1f136f) {
      vscode['window'][_0x6993cd(0x1da)]('错误:\x20' + _0x1f136f[_0x6993cd(0x1e6)]);
    }
  } ['dispose']() {
    const _0x124c37 = _0x3eb09c;
    this[_0x124c37(0x181)] = ![], this[_0x124c37(0x1b1)][_0x124c37(0x19e)](_0x124c37(0x189));
  }
}

function _0x2025(_0x4f05e9, _0x1bafa0) {
  const _0x24f50b = _0x24f5();
  return _0x2025 = function (_0x202511, _0x2de70c) {
    _0x202511 = _0x202511 - 0x170;
    let _0x5ba537 = _0x24f50b[_0x202511];
    return _0x5ba537;
  }, _0x2025(_0x4f05e9, _0x1bafa0);
}
module[_0x3eb09c(0x1a2)] = AugmentCustomFeatures;