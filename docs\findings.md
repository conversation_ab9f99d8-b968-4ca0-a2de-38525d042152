# VSCode 插件逆向分析报告

## 概述

这是一个 VSCode 插件的逆向分析，主要分析两个核心文件：

- `extension/out/token-manager-simple.js` - Token 管理器
- `extension/out/custom-features.js` - 自定义功能模块

## 插件基本信息

- **插件名称**: Augment 无感上号版
- **发布者**: Augment
- **版本**: 0.524.1
- **主入口**: `./out/extension.js`

## 核心架构分析

### 1. 插件注入机制

#### 1.1 主扩展文件注入点

在 `extension/out/extension.js` 的末尾（第 204445-204516 行），发现了两个模块的注入：

```javascript
// 注入自定义功能模块
const AugmentCustomFeatures = require("./custom-features");
let customFeatures = null;

// 注入Token管理器模块
const SimpleTokenManagerIntegration = require("./token-manager-simple");
let tokenManagerIntegration = null;
```

#### 1.2 初始化流程

1. **自定义功能初始化**：在 `customActivate` 函数中立即初始化
2. **Token 管理器初始化**：延迟 1 秒后初始化，避免与主插件冲突

### 2. Token 管理器 (token-manager-simple.js) 分析

#### 2.1 主要类结构

- `SimpleTokenApiService` - API 服务类
- `SimpleSettingsManager` - 设置管理类
- `SimpleSettingsWebViewProvider` - 设置界面提供者
- `SimpleTokenManager` - Token 管理核心类
- `SimpleTokenManagerSidebarProvider` - 侧边栏界面提供者
- `SimpleTokenManagerIntegration` - 集成管理类

#### 2.2 认证机制分析

**API 端点**：

- 基础 URL: `https://augmenttoken.159email.shop` (默认)
- 用户验证: `/api/user/verify`
- 获取 Token 列表: `/api/user/available-tokens`

**认证流程**：

1. 用户输入 `userToken` (用户登录 Token)
2. 调用 `/api/user/verify` 验证用户身份
3. 验证成功后获取可用的 `accessToken` 列表
4. 选择并存储 `accessToken` 到 VSCode secrets

**Token 类型**：

- `userToken`: 用户登录凭证，用于验证身份
- `accessToken`: 实际的访问令牌，用于 API 调用
- `tenantURL`: 租户 URL，默认为 `https://d5.api.augmentcode.com/`

#### 2.3 存储机制

- 使用 VSCode 的 `secrets` API 存储敏感信息
- 存储键名: `augment.sessions`
- 数据格式: JSON 字符串，包含 `accessToken`, `tenantURL`, `scopes`

#### 2.4 UI 界面

- 创建独立的侧边栏视图 "Token Manager"
- 提供用户验证、Token 管理、机器码更新等功能
- 使用内嵌 HTML + JavaScript 实现交互

### 3. 自定义功能模块 (custom-features.js) 分析

#### 3.1 主要功能

- 注册自定义命令: `augment.custom.newpool`
- 提供 Token 获取、设置、更新功能
- 管理会话数据和机器码

#### 3.2 核心方法

- `getAccessToken()`: 获取当前存储的访问令牌
- `updateAccessToken()`: 更新访问令牌
- `updateSessionsData()`: 更新完整会话数据
- `handleUpdateMachineCode()`: 更新机器码

#### 3.3 数据管理

- 同样使用 `augment.sessions` 键存储数据
- 支持快速更新（仅更新 accessToken）和完整更新（更新所有字段）

## 安全分析

### 1. 认证安全

- 使用 Bearer Token 认证方式
- API 请求包含固定的 API Key: `augment_external_v1_2024`
- 用户 Token 和访问 Token 分离，提高安全性

### 2. 数据存储安全

- 使用 VSCode 官方的 secrets API 存储敏感数据
- 数据加密存储在系统密钥环中

### 3. 网络通信

- 支持 HTTPS 和 HTTP 协议
- 包含超时机制（30 秒）
- 使用标准的 fetch API 和 Node.js http 模块

## 发现的关键信息

### 1. API 认证机制

- **认证方式**: Bearer Token + 固定 API Key
- **API Key**: `augment_external_v1_2024`
- **用户 Agent**: `VSCode-AugmentTokenManager/1.0.0`

### 2. 数据流向

```
用户输入userToken → 验证API → 获取accessToken列表 → 存储到VSCode secrets → 用于后续API调用
```

### 3. 配置信息

- 默认 API 基础 URL 可配置
- 默认租户 URL 可配置
- 支持重置为默认设置

## 代码混淆分析

### 1. 混淆技术

两个文件都使用了相同的 JavaScript 混淆技术：

- **字符串数组混淆**: 所有字符串被提取到数组中，通过索引访问
- **函数名混淆**: 使用 `_0x592fd3`, `_0x2a3b`, `_0x3eb09c` 等混淆名称
- **控制流混淆**: 使用复杂的数字计算和循环来混淆执行流程

### 2. 关键字符串解析

#### Token Manager 关键字符串：

- `'VSCode-AugmentTokenManager/1.0.0'` - HTTP User Agent
- `'https://augmenttoken.159email.shop'` - 主 API 服务器
- `'https://d5.api.augmentcode.com/'` - 默认租户 URL
- `'/api/user/verify'` - 用户验证端点
- `'/api/user/available-tokens'` - 获取 Token 列表端点
- `'augment_external_v1_2024'` - 固定 API 密钥
- `'Bearer\x20'` - 认证头前缀
- `'augment.sessions'` - VSCode 存储键名

#### Custom Features 关键字符串：

- `'augment.custom.newpool'` - 自定义命令 ID
- `'sessionId更新成功！新值:'` - 机器码更新消息
- `'获取\x20accessToken'` - 获取令牌功能
- `'设置\x20accessToken'` - 设置令牌功能
- `'更新机器码'` - 机器码管理功能

## 完整认证流程分析

### 1. 用户认证阶段

```
用户输入userToken → POST /api/user/verify → 验证用户身份 → 返回用户信息
```

### 2. Token 获取阶段

```
使用userToken → GET /api/user/available-tokens → 获取可用accessToken列表 → 用户选择
```

### 3. Token 存储阶段

```
选择的accessToken → 组装会话数据 → 存储到VSCode secrets → 供后续使用
```

### 4. 会话数据结构

```json
{
  "accessToken": "实际的访问令牌",
  "tenantURL": "租户URL，默认为https://d5.api.augmentcode.com/",
  "scopes": ["augment_external_v1_2024"]
}
```

## 插件注入机制详解

### 1. 注入时机

- 在原始插件的 `activate` 函数执行后注入
- Custom Features 立即初始化
- Token Manager 延迟 1 秒初始化（避免冲突）

### 2. 注入方式

- 通过修改 `extension.js` 文件末尾添加注入代码
- 保留原始插件的所有功能
- 添加新的 activate/deactivate 钩子

### 3. 生命周期管理

- 独立的初始化和清理流程
- 错误隔离，不影响原始插件运行
- 支持热重载和调试

## 安全风险评估

### 1. 网络通信风险

- **中等风险**: 使用第三方 API 服务器 `augmenttoken.159email.shop`
- **低风险**: 支持 HTTPS 加密通信
- **注意**: 固定 API 密钥可能被滥用

### 2. 数据存储风险

- **低风险**: 使用 VSCode 官方 secrets API
- **低风险**: 数据加密存储在系统密钥环
- **注意**: 敏感 Token 存储在本地

### 3. 代码注入风险

- **高风险**: 修改了原始插件代码
- **中等风险**: 可能影响插件更新和完整性
- **注意**: 混淆代码难以审计

## 功能模块职责分工

### Token Manager (token-manager-simple.js)

- **核心职责**: API 通信、认证管理
- **UI 组件**: 侧边栏 Token 管理界面
- **数据管理**: Token 存储和检索
- **网络请求**: 与认证服务器通信

### Custom Features (custom-features.js)

- **核心职责**: 用户交互、便捷操作
- **命令注册**: `augment.custom.newpool` 命令
- **快捷功能**: Token 查看、复制、更新
- **机器码管理**: 设备标识符生成和更新

## 发现的隐藏功能

### 1. 调试功能

- 注册了 `augment.debug.tokenManager` 调试命令
- 可能用于开发者调试和测试

### 2. 机器码管理

- 使用 `crypto.randomUUID()` 生成设备标识
- 存储在 VSCode 的 globalState 中
- 键名为 `sessionId`

### 3. 心跳机制

- 代码中提到了心跳相关功能（已被移除）
- 可能原本用于保持连接活跃

## UI 界面和资源文件分析

### 1. Token Manager 侧边栏

- **图标文件**: `extension/media/token-manager-icon.svg`
- **图标描述**: 一个卡通风格的钥匙/令牌管理图标
- **容器 ID**: `augment-token-manager`
- **视图 ID**: `augmentTokenManager`
- **显示名称**: "Token Manager"

### 2. 界面配置

在 `package.json` 中发现了完整的 UI 配置：

```json
{
  "viewsContainers": {
    "activitybar": [
      {
        "icon": "media/token-manager-icon.svg",
        "id": "augment-token-manager",
        "title": "Token Manager"
      }
    ]
  },
  "views": {
    "augment-token-manager": [
      {
        "id": "augmentTokenManager",
        "name": "Token Manager",
        "type": "webview"
      }
    ]
  }
}
```

### 3. 自定义命令

- **命令 ID**: `augment.custom.newpool`
- **功能**: 提供 Token 管理的快捷操作菜单
- **调试命令**: `augment.debug.tokenManager`

### 4. 界面特点

- 独立的活动栏图标，与原始 Augment 插件并列显示
- 使用 webview 技术实现复杂的用户界面
- 支持中文界面，面向中文用户
- 包含学习交流 QQ 群信息：1017212982

## 插件修改方式分析

### 1. 非侵入式注入

- 在原始插件的 `extension.js` 末尾添加注入代码
- 保留所有原始功能，仅添加新功能
- 使用独立的模块和命名空间

### 2. 配置文件修改

- 修改 `package.json` 添加新的视图容器和视图
- 添加 Token Manager 的图标和界面配置
- 未在 commands 部分注册自定义命令（动态注册）

### 3. 资源文件添加

- 添加 `token-manager-icon.svg` 图标文件
- 图标采用卡通风格，与原插件风格一致

## 技术实现细节

### 1. 模块化设计

- 两个独立的 JavaScript 模块
- 清晰的职责分工和接口设计
- 支持独立的生命周期管理

### 2. 错误处理

- 完善的 try-catch 错误捕获
- 错误隔离，不影响原始插件运行
- 用户友好的错误提示

### 3. 异步处理

- 大量使用 async/await 处理异步操作
- 网络请求超时控制
- 重试机制和降级处理

## 目标用户分析

### 1. 用户群体

- 中文用户（界面为中文）
- Augment Code 的学习研究者
- 需要便捷 Token 管理的开发者

### 2. 使用场景

- 学习和研究 Augment Code API
- 多账户 Token 管理
- 快速切换不同的访问令牌

### 3. 社区支持

- 提供 QQ 群进行学习交流
- 标注"仅供学习使用"

## "无感换号"机制深度分析

### 1. 核心实现原理

**"无感换号"的本质**：

- 劫持原始 Augment 插件的认证存储位置
- 将第三方服务器获取的 token 伪装成正常的用户登录 token
- 实现账号的热切换，无需重启插件或重新登录

### 1.1 为什么要"劫持"而不是解密？

**技术限制**：

- VSCode 的 secrets API 使用系统级加密（Windows Credential Manager、macOS Keychain、Linux Secret Service）
- 这些加密无法被第三方插件破解或解密
- 即使获得加密数据，也无法还原原始内容

**劫持的可行性**：

- VSCode secrets API 允许任何插件使用任何键名存储数据
- 不同插件可以使用相同的键名，后写入的会覆盖先写入的
- 原始插件读取时会获得最新写入的数据，无法察觉来源变化

### 1.2 劫持的具体实现方式

**关键发现**：

- 原始 Augment 插件使用 `'augment.sessions'` 键存储认证信息
- Token Manager 插件使用相同的键名进行"覆盖式劫持"
- 通过 `context.secrets.store('augment.sessions', JSON.stringify(sessionData))` 实现

**劫持流程**：

```
1. 发现目标键名 → 2. 准备伪装数据 → 3. 覆盖存储 → 4. 原始插件无感读取
```

### 2. 完整的无感换号流程

#### 阶段 1：用户认证

```
用户输入userToken → POST /api/user/verify → 验证用户身份 → 返回用户信息
```

#### 阶段 2：Token 获取

```
使用userToken → GET /api/user/available-tokens → 获取可用accessToken列表
```

**API 返回数据结构**：

```json
{
  "data": {
    "tokens": [
      {
        "id": "token_id",
        "accessToken": "实际的访问令牌",
        "tenantURL": "租户URL",
        "use_time": "使用时间",
        "created_at": "创建时间",
        "updated_at": "更新时间"
      }
    ]
  }
}
```

#### 阶段 3：Token 注入

```
选择accessToken → 组装会话数据 → 写入VSCode secrets → 原始插件无感知使用
```

**注入的数据格式**：

```json
{
  "accessToken": "从API获取的访问令牌",
  "tenantURL": "租户URL，默认https://d5.api.augmentcode.com/",
  "scopes": ["augment_external_v1_2024"]
}
```

#### 阶段 4：一键更新功能

- **功能名称**: "🚀 一键更新 Token"
- **实现逻辑**: 自动获取可用 token 列表，随机选择一个进行更新
- **用户体验**: 一键操作，无需手动选择
- **成功提示**: "一键更新成功！随机选择了第 X 个 token"

### 3. 关键注入点分析

#### 存储劫持

- **目标位置**: VSCode secrets API
- **存储键名**: `'augment.sessions'`
- **劫持原理**: 原始 Augment 插件从这个键读取认证信息

#### 数据格式兼容

- **accessToken**: 实际的 API 访问令牌
- **tenantURL**: 指向 Augment Code 的 API 服务器
- **scopes**: 固定为 `["augment_external_v1_2024"]`

### 4. 机器码管理机制

#### 设备标识生成

- **生成方式**: `crypto.randomUUID()`
- **存储位置**: VSCode globalState
- **存储键名**: `'sessionId'`
- **用途**: 设备唯一标识，可能用于 token 绑定

#### 机器码更新功能

- **触发方式**: 用户点击"🔧 更新机器码"按钮
- **更新逻辑**: 生成新的 UUID 并保存
- **成功提示**: "sessionId 更新成功！新值: [新 UUID]"

### 5. 无感换号的技术优势

#### 对用户的优势

1. **无需重启**: 切换账号无需重启 VSCode 或插件
2. **热切换**: 实时生效，立即可用
3. **批量管理**: 可管理多个账号的 token
4. **自动化**: 一键更新功能，减少手动操作

#### 对原始插件的透明性

1. **完全兼容**: 使用相同的数据格式和存储位置
2. **无感知**: 原始插件无法察觉 token 来源的变化
3. **功能完整**: 不影响原始插件的任何功能

### 6. 安全风险评估

#### 高风险点

1. **第三方依赖**: 依赖外部 API 服务器 `augmenttoken.159email.shop`
2. **token 来源**: 无法验证第三方 token 的合法性
3. **数据泄露**: 敏感 token 通过网络传输

#### 中等风险点

1. **API 密钥**: 固定的 API 密钥可能被滥用
2. **机器码**: 设备标识可能被追踪

#### 低风险点

1. **本地存储**: 使用 VSCode 官方加密存储
2. **错误隔离**: 不影响原始插件稳定性

## 下一步分析建议

1. **网络流量分析**: 监控插件的实际网络请求
2. **API 端点探索**: 测试发现的 API 端点的完整功能
3. **Token 使用追踪**: 了解 accessToken 在实际场景中的使用
4. **安全测试**: 评估 API 密钥和认证机制的安全性
5. **原始插件分析**: 研究这些 Token 如何与原始 Augment 插件集成
6. **界面测试**: 实际运行插件，测试 UI 界面的完整功能
7. **API 服务器分析**: 研究 `augmenttoken.159email.shop` 服务器的完整功能
8. **反混淆分析**: 对混淆代码进行深度反混淆，了解更多实现细节
9. **Token 生命周期**: 研究 token 的有效期、刷新机制和失效处理
10. **原始插件集成点**: 找到原始插件中读取 `augment.sessions` 的具体位置
