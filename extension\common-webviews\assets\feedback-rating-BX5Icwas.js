import{z as j,A as q,C as e,Y as d,m as D,D as G,J as H,L as y,F as p,G as v,H as I,b as n,N as C,_ as b,a3 as J,t as V,I as Y}from"./SpinnerAugment-BY2Lraps.js";import{C as _}from"./copy-DdR1jezc.js";import{S as B}from"./TextAreaAugment-BkN7aH6v.js";var E=H('<span class="c-copy-button svelte-tq93gm"><!></span>');function R(s,t){const g=j(t);q(t,!1);let x=e(t,"size",8,1),w=e(t,"variant",8,"ghost-block"),z=e(t,"color",8,"neutral"),i=e(t,"text",24,()=>{}),N=e(t,"tooltip",8,"Copy"),h=e(t,"stickyColor",8,!1),F=e(t,"onCopy",8,async()=>{if(i()!==void 0){d(l,!0);try{await Promise.all([navigator.clipboard.writeText(typeof i()=="string"?i():await i()()),(o=250,new Promise(a=>setTimeout(a,o,c)))])}finally{d(l,!1)}var o,c;return"success"}}),L=e(t,"tooltipNested",24,()=>{}),l=D(!1);G();var f=E(),P=V(f);const T=J(()=>({neutral:N(),success:"Copied!"}));B(P,{get defaultColor(){return z()},get size(){return x()},get variant(){return w()},get loading(){return b(l)},get stickyColor(){return h()},get tooltip(){return b(T)},stateVariant:{success:"soft"},onClick:F(),icon:y(()=>!g.text),get tooltipNested(){return L()},children:(o,c)=>{var a=p(),u=v(a);C(u,t,"text",{},null),n(o,a)},$$slots:{default:!0,iconLeft:(o,c)=>{var a=p(),u=v(a),$=r=>{var m=p(),S=v(m);C(S,t,"icon",{},null),n(r,m)},A=r=>{_(r,{})};I(u,r=>{y(()=>g.icon)?r($):r(A,!1)}),n(o,a)}}}),n(s,f),Y()}var K=(s=>(s[s.unset=0]="unset",s[s.positive=1]="positive",s[s.negative=2]="negative",s))(K||{});export{R as C,K as F};
