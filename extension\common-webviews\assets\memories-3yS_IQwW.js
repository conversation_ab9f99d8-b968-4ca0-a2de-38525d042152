import{l as Oe,f as Be,a as De,t as b,b as n,A as fe,C as ie,ak as Ie,w as ee,X as me,Y as g,a4 as j,m as h,_ as e,K as pe,$ as He,D as he,F as Me,G as ne,H as te,I as $e,a2 as Se,J,am as Re,V as q,Q as Le,W as be,L as W,a3 as N,M as se,a5 as Pe,T as Ee,a6 as Ve,P as We,al as _e,az as je}from"./SpinnerAugment-BY2Lraps.js";import"./design-system-init-DkEuonq_.js";import{h as Ue,c as U,W as ye,e as qe,i as Je}from"./IconButtonAugment-B8y0FMb_.js";import{O as Qe}from"./OpenFileButton-DL6LWlGL.js";import{S as Xe}from"./TextAreaAugment-BkN7aH6v.js";import{C as Ye}from"./check-nWNl4dzZ.js";import{C as Te,E as ke,D as _,R as Ke,M as ge,f as Ze,g as et,h as tt}from"./index-78K9HN9C.js";import{M as we}from"./message-broker-BauNv3yh.js";import{M as st}from"./MarkdownEditor-DyMkuzW7.js";import{B as Fe}from"./ButtonAugment-BoJU5mQc.js";import{C as Ge}from"./chevron-down-CVLGkBkY.js";import{F as ot}from"./Filespan-7QsRDmJG.js";import{T as ze,a as re}from"./CardAugment-BaFOe6RO.js";import"./chat-model-context-C9JFkoqk.js";import"./index-C4gKbsWy.js";import"./index-CoHT-xzg.js";import"./remote-agents-client-XI3B217g.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-3WeVBTrk.js";import"./BaseTextInput-C64uUToe.js";import"./async-messaging-BnOo7nYC.js";import"./focusTrapStack-CAuiPHBF.js";import"./isObjectLike-DuRpH5zX.js";var at=Be("<svg><!></svg>");function xe(Q,I){const $=Oe(I,["children","$$slots","$$events","$$legacy"]);var S=at();De(S,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...$}));var i=b(S);Ue(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M440.6 273.4c4.7-4.5 7.4-10.8 7.4-17.4s-2.7-12.8-7.4-17.4l-176-168c-9.6-9.2-24.8-8.8-33.9.8s-8.8 24.8.8 33.9L364.1 232H24c-13.3 0-24 10.7-24 24s10.7 24 24 24h340.1L231.4 406.6c-9.6 9.2-9.9 24.3-.8 33.9s24.3 9.9 33.9.8l176-168z"/>',!0),n(Q,S)}var nt=J("<!> <!>",1),rt=J('<div class="rules-dropdown-content svelte-18wohv"><!> <!></div>'),it=J("<!> <!>",1);function lt(Q,I){fe(I,!1);const[$,S]=Se(),i=()=>j(s,"$rulesFiles",$),O=()=>j(L,"$selectedRule",$),y=()=>j(e(F),"$focusedIndex",$),R=h(),X=h(),Y=h();let oe=ie(I,"onRuleSelected",8),ae=ie(I,"disabled",8,!1);const le=new we(U),ce=new Te,l=new ke(U,le,ce),s=ee([]),w=ee(!0),L=ee(void 0);let F=h(void 0),B=h(()=>{});Ie(()=>{(async function(){try{w.set(!0);const u=await l.findRules("",100);s.set(u)}catch(u){console.error("Failed to load rules:",u),s.set([])}finally{w.set(!1)}})();const d=u=>{var E;((E=u.data)==null?void 0:E.type)===ye.getRulesListResponse&&(s.set(u.data.data||[]),w.set(!1))};return window.addEventListener("message",d),()=>{window.removeEventListener("message",d)}});let D=h(),x=h(!1);function de(d){g(x,d)}me(()=>i(),()=>{g(R,i().length>0)}),me(()=>(pe(ae()),e(R)),()=>{g(X,ae()||!e(R))}),me(()=>e(R),()=>{g(Y,e(R)?"Move highlighted text to a .augment/rules file":"Please add at least 1 file to .augment/rules and reload VSCode")}),He(),he();var c=Me(),K=ne(c),Z=d=>{var u=Me(),E=ne(u),ue=v=>{_.Root(v,{onOpenChange:de,get requestClose(){return e(B)},set requestClose(a){g(B,a)},get focusedIndex(){return e(F)},set focusedIndex(a){Pe(g(F,a),"$focusedIndex",$)},children:(a,m)=>{var f=it(),p=ne(f);_.Trigger(p,{children:(T,ve)=>{const P=N(()=>(pe(re),W(()=>[re.Hover]))),V=N(()=>!e(x)&&void 0);Re(ze(T,{get content(){return e(Y)},get triggerOn(){return e(P)},side:"top",get open(){return e(V)},children:(k,C)=>{Fe(k,{color:"neutral",variant:"soft",size:1,get disabled(){return e(X)},children:(r,o)=>{var M=q();Le(()=>be(M,(O(),W(()=>O()?O().path:"Rules")))),n(r,M)},$$slots:{default:!0,iconLeft:(r,o)=>{xe(r,{slot:"iconLeft"})},iconRight:(r,o)=>{Ge(r,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),k=>g(D,k),()=>e(D))},$$slots:{default:!0}});var H=se(p,2);_.Content(H,{side:"bottom",align:"start",children:(T,ve)=>{var P=rt(),V=b(P);qe(V,1,i,Je,(r,o,M)=>{const A=N(()=>y()===M);_.Item(r,{onSelect:()=>function(G){L.set(G),oe()(G),e(B)()}(e(o)),get highlight(){return e(A)},children:(G,z)=>{ot(G,{get filepath(){return e(o),W(()=>e(o).path)}})},$$slots:{default:!0}})});var k=se(V,2),C=r=>{var o=nt(),M=ne(o);_.Separator(M,{});var A=se(M,2);_.Label(A,{children:(G,z)=>{Ee(G,{size:1,color:"neutral",children:(Ae,vt)=>{var Ce=q();Le(Ne=>be(Ce,Ne),[()=>(i(),y(),W(()=>`Move to ${i()[y()].path}`))],N),n(Ae,Ce)},$$slots:{default:!0}})},$$slots:{default:!0}}),n(r,o)};te(k,r=>{y(),i(),W(()=>y()!==void 0&&i()[y()])&&r(C)}),n(T,P)},$$slots:{default:!0}}),n(a,f)},$$slots:{default:!0},$$legacy:!0})},t=v=>{const a=N(()=>(pe(re),W(()=>[re.Hover])));Re(ze(v,{get content(){return e(Y)},get triggerOn(){return e(a)},side:"top",children:(m,f)=>{Fe(m,{color:"neutral",variant:"soft",size:1,disabled:!0,children:(p,H)=>{var T=q("Rules");n(p,T)},$$slots:{default:!0,iconLeft:(p,H)=>{xe(p,{slot:"iconLeft"})},iconRight:(p,H)=>{Ge(p,{slot:"iconRight"})}}})},$$slots:{default:!0},$$legacy:!0}),m=>g(D,m),()=>e(D))};te(E,v=>{e(R)?v(ue):v(t,!1)}),n(d,u)};te(K,d=>{j(w,"$loading",$)||d(Z)}),n(Q,c),$e(),S()}var ct=J('<div slot="iconLeft" class="c-move-text-btn__left_icon svelte-1yddhs6"><!></div>'),dt=J('<div class="l-file-controls svelte-1yddhs6" slot="header"><div class="l-file-controls-left svelte-1yddhs6"><div class="c-move-text-btn svelte-1yddhs6"><!></div> <div class="c-move-text-btn svelte-1yddhs6"><!></div></div> <div class="l-file-controls-right svelte-1yddhs6"><!></div></div>'),ut=J('<div class="c-memories-container svelte-1vchs21"><!></div>');je(function(Q,I){fe(I,!1);const[$,S]=Se(),i=()=>j(R,"$editorContent",$),O=()=>j(X,"$editorPath",$),y=new we(U),R=ee(null),X=ee(null),Y={handleMessageFromExtension(l){const s=l.data;if(s&&s.type===ye.loadFile){if(s.data.content!==void 0){const w=s.data.content.replace(/^\n+/,"");R.set(w)}s.data.pathName&&X.set(s.data.pathName)}return!0}};Ie(()=>{y.registerConsumer(Y),U.postMessage({type:ye.memoriesLoaded})}),Ve(()=>{y.dispose()}),he();var oe=ut();We("message",_e,function(...l){var s;(s=y.onMessageFromExtension)==null||s.apply(this,l)});var ae=b(oe),le=l=>{(function(s,w){fe(w,!1);let L=ie(w,"text",12),F=ie(w,"path",8);const B=new we(U),D=new Te,x=new ke(U,B,D),de=new Ke(B);let c=h(""),K=h(0),Z=h(0),d=h("neutral");const u=async()=>{F()&&x.saveFile({repoRoot:"",pathName:F(),content:L()})};async function E(t){if(!e(c))return;let v,a,m;const f=e(c).slice(0,20);if(t==="userGuidelines"?(v="Move Content to User Guidelines",a=`Are you sure you want to move the selected content "${f}" to your user guidelines?`,m=ge.userGuidelines):t==="augmentGuidelines"?(v="Move Content to Workspace Guidelines",a=`Are you sure you want to move the selected content "${f}" to workspace guidelines?`,m=ge.augmentGuidelines):(v="Move Content to Rule",a=`Are you sure you want to move the selected content "${f}" to rule file "${t.rule.path}"?`,m=ge.rules),!await x.openConfirmationModal({title:v,message:a,confirmButtonText:"Move",cancelButtonText:"Cancel"}))return;t==="userGuidelines"?x.updateUserGuidelines(e(c)):t==="augmentGuidelines"?x.updateWorkspaceGuidelines(e(c)):(await de.updateRuleContent({type:t.rule.type,path:t.rule.path,content:t.rule.content+`

`+e(c),description:t.rule.description}),x.showNotification({message:`Moved content "${f}" to rule file "${t.rule.path}"`,type:"info",openFileMessage:{repoRoot:"",pathName:`${et}/${tt}/${t.rule.path}`}}));const p=L().substring(0,e(K))+L().substring(e(Z));return L(p),await u(),x.reportAgentSessionEvent({eventName:Ze.memoriesMove,conversationId:"",eventData:{memoriesMoveData:{target:m}}}),"success"}async function ue(t){await E({rule:t})}he(),st(s,{saveFunction:u,variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get selectedText(){return e(c)},set selectedText(t){g(c,t)},get selectionStart(){return e(K)},set selectionStart(t){g(K,t)},get selectionEnd(){return e(Z)},set selectionEnd(t){g(Z,t)},get value(){return L()},set value(t){L(t)},$$slots:{header:(t,v)=>{var a=dt(),m=b(a),f=b(m),p=b(f);const H=N(()=>!e(c));Xe(p,{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to user guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:()=>E("userGuidelines"),get disabled(){return e(H)},stickyColor:!1,persistOnTooltipClose:!0,replaceIconOnSuccess:!0,size:1,get state(){return e(d)},set state(C){g(d,C)},children:(C,r)=>{var o=q("User Guidelines");n(C,o)},$$slots:{default:!0,iconLeft:(C,r)=>{var o=ct(),M=b(o),A=z=>{Ye(z,{})},G=z=>{xe(z,{})};te(M,z=>{e(d)==="success"?z(A):z(G,!1)}),n(C,o)}},$$legacy:!0});var T=se(f,2),ve=b(T);const P=N(()=>!e(c));lt(ve,{onRuleSelected:ue,get disabled(){return e(P)}});var V=se(m,2),k=b(V);Qe(k,{size:1,get path(){return F()},variant:"soft",onOpenLocalFile:async()=>(x.openFile({repoRoot:"",pathName:F()}),"success"),$$slots:{text:(C,r)=>{Ee(C,{slot:"text",size:1,children:(o,M)=>{var A=q("Augment-Memories.md");n(o,A)},$$slots:{default:!0}})}}}),n(t,a)}},$$legacy:!0}),$e()})(l,{get text(){return i()},get path(){return O()}})},ce=l=>{var s=q("Loading memories...");n(l,s)};te(ae,l=>{i()!==null&&O()!==null?l(le):l(ce,!1)}),n(Q,oe),$e(),S()},{target:document.getElementById("app")});
