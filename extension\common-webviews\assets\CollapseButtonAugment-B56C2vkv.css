.c-collapsible.svelte-zpen55{--collapsible-panel-background: var(--ds-color-neutral-a2);--collapsible-panel-border-color: var(--augment-border-color);--collapsible-panel-border: 1px solid var(--collapsible-panel-border-color);--collapsible-panel-radius: var(--ds-radius-2);display:flex;flex-direction:column}.c-collapsible__body--should-hide.svelte-zpen55{display:none}.c-collapsible__header.svelte-zpen55{display:flex;align-items:center}.c-collapsible__header-inner.svelte-zpen55{border-radius:var(--collapsible-panel-radius) var(--collapsible-panel-radius) 0 0;background:var(--collapsible-panel-background);border:var(--collapsible-panel-border);width:100%;position:relative}.is-header-stuck.c-collapsible__header-inner.svelte-zpen55:not(.is-collapsed):before{content:"";position:absolute;top:calc(-1 * var(--sticky-header-top) - 1px);bottom:0;left:calc(-1 * var(--ds-spacing-1));width:calc(100% + var(--ds-spacing-1) * 2);background:var(--augment-window-background);pointer-events:none;border:0;z-index:-1}.c-collapsible__header-inner.is-collapsed.svelte-zpen55{border-radius:var(--collapsible-panel-radius)}.c-collapsible__header.is-sticky.svelte-zpen55{position:sticky;top:var(--sticky-header-top);z-index:var(--z-block-container-sticky-header)}.c-collapsible__content.svelte-zpen55{background:var(--collapsible-panel-background);display:grid;grid-template-rows:1fr;transition:grid-template-rows .2s ease-in-out;overflow:clip;border-bottom-left-radius:var(--collapsible-panel-radius);border-bottom-right-radius:var(--collapsible-panel-radius);border:var(--collapsible-panel-border);border-top:0}.c-collapsible__content.svelte-zpen55.is-collapsed{border-bottom:none}.c-collapsible__content-inner.svelte-zpen55{overflow:hidden;min-height:0;display:flex;flex-direction:column}.c-collapsible__content-inner-height-wrapper.svelte-zpen55{min-height:var(--last-known-child-height, 0)}.c-collapsible__body.svelte-zpen55{flex:1}.c-collapsible__footer.svelte-zpen55{border-top:var(--collapsible-panel-border)}.c-collapsible.is-collapsed.svelte-zpen55 .c-collapsible__content:where(.svelte-zpen55){grid-template-rows:0fr}.c-collapsible.is-collapsed.svelte-zpen55 .c-collapsible__header:where(.svelte-zpen55){border-radius:var(--collapsible-panel-radius);border-bottom:none}.c-collapsible.is-collapsed.svelte-zpen55 .c-collapsible__footer:where(.svelte-zpen55){border-top:none}.c-collapsible.svelte-zpen55:not(.is-expandable){cursor:default}.c-collapsible.svelte-zpen55:not(.is-expandable) .c-collapsible__header:where(.svelte-zpen55){cursor:default}.is-expandable.svelte-zpen55 .c-collapsible__header-inner:where(.svelte-zpen55):hover{--collapsible-icon-visibility: hidden;--collapsible-icon-expand-visibility: visible}.c-collapsible.is-expandable.svelte-zpen55 .c-collapsible__header-inner:where(.svelte-zpen55):has([data-collapsible-button=false]:hover),.c-collapsible.is-expandable.svelte-zpen55 .c-collapsible__header-inner:where(.svelte-zpen55):has(.c-collapsible-button-false:hover){--collapsible-icon-visibility: visible;--collapsible-icon-expand-visibility: hidden}.c-collapse-button-augment__icon.svelte-hw7s17{display:flex;--icon-size: 12px}
